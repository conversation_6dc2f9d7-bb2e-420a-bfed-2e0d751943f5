import * as FileSystem from 'expo-file-system';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface VideoPromo {
  id: number;
  nome: string;
  descricao: string;
  filial: string;
  data_inical: string;
  data_final: string;
  promo: boolean;
  userId: number;
  excluido: boolean;
  created_at: string;
  updated_at: string;
}

export class VideoSyncService {
  private static instance: VideoSyncService;
  private videosDir: string;
  private apiUrl: string = '';
  private syncInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.videosDir = FileSystem.documentDirectory + 'promotional_videos/';
  }

  public static getInstance(): VideoSyncService {
    if (!VideoSyncService.instance) {
      VideoSyncService.instance = new VideoSyncService();
    }
    return VideoSyncService.instance;
  }

  public async initialize(apiUrl: string): Promise<void> {
    this.apiUrl = apiUrl;
    
    // Criar diretório de vídeos se não existir
    const dirInfo = await FileSystem.getInfoAsync(this.videosDir);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(this.videosDir, { intermediates: true });
    //  console.log('🎬 Diretório de vídeos promocionais criado');
    }

    // Iniciar verificação periódica a cada 1 minuto
    this.startPeriodicSync();
  }

  public async fetchAvailableVideos(): Promise<VideoPromo[]> {
    try {
      if (!this.apiUrl) {
        throw new Error('API URL não configurada');
      }

      const url = `${this.apiUrl}/buscarprecovideo/false`;
    //  console.log('🎬 Buscando vídeos disponíveis na API:', url);
      const response = await axios.get(url);
    //  console.log('🎬 Resposta da API recebida:', response.data);
      
      // Verificar se a resposta tem a estrutura esperada
      if (response.data && response.data.videos && Array.isArray(response.data.videos)) {
      //  console.log('🎬 Vídeos disponíveis encontrados:', response.data.videos.length);
        return response.data.videos;
      } else if (Array.isArray(response.data)) {
        // Fallback caso a API retorne diretamente um array
      //  console.log('🎬 Vídeos disponíveis encontrados (array direto):', response.data.length);
        return response.data;
      } else {
      //  console.log('🎬 Estrutura de resposta inesperada:', response.data);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar vídeos disponíveis:', error);
      return [];
    }
  }

  public async syncVideos(): Promise<void> {
    try {
      console.log('🎬 Iniciando sincronização de vídeos promocionais...');
      
      // 1. Buscar vídeos disponíveis
      const availableVideos = await this.fetchAvailableVideos();
      
      if (availableVideos.length === 0) {
        console.log('🎬 Nenhum vídeo promocional disponível na API');
        // Limpar vídeos locais se não há vídeos na API
        await this.clearExistingVideos();
        return;
      }

      console.log(`🎬 ${availableVideos.length} vídeos encontrados na API`);

      // 2. Limpar vídeos existentes
      await this.clearExistingVideos();

      // 3. Baixar novos vídeos
      let downloadedCount = 0;
      for (const video of availableVideos) {
        const success = await this.downloadVideo(video.nome);
        if (success) {
          downloadedCount++;
        }
      }

      console.log(`✅ ${downloadedCount}/${availableVideos.length} vídeos baixados com sucesso`);

      // 4. Salvar timestamp da última sincronização
      await AsyncStorage.setItem('@videos_last_sync', new Date().toISOString());
      
      console.log('🎬 Sincronização de vídeos concluída com sucesso');
    } catch (error) {
      console.error('❌ Erro na sincronização de vídeos:', error);
    }
  }

  private async clearExistingVideos(): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.videosDir);
      if (dirInfo.exists) {
        const files = await FileSystem.readDirectoryAsync(this.videosDir);
        
        if (files.length > 0) {
          console.log(`🗑️ Removendo ${files.length} vídeos existentes...`);
          
          for (const file of files) {
            const filePath = this.videosDir + file;
            await FileSystem.deleteAsync(filePath);
            console.log(`🗑️ Vídeo removido: ${file}`);
          }
        } else {
          console.log('📂 Diretório de vídeos já está vazio');
        }
      }
    } catch (error) {
      console.error('❌ Erro ao limpar vídeos existentes:', error);
    }
  }

  private async downloadVideo(videoName: string): Promise<boolean> {
    try {
      if (!this.apiUrl) {
        throw new Error('API URL não configurada');
      }

      // Se o nome não tem extensão, extrair apenas o nome base para a URL
      const videoNameWithoutExt = videoName.includes('.') ? videoName.split('.')[0] : videoName;
      const downloadUrl = `${this.apiUrl}/getUniqueBuscarPrecoVideo/${videoNameWithoutExt}`;
      
      // Garantir que o arquivo local tenha extensão .mp4
      const localFileName = videoName.includes('.') ? videoName : `${videoName}.mp4`;
      const localPath = this.videosDir + localFileName;
      
      console.log(`⬇️ Baixando vídeo: ${localFileName}`);
      console.log(`🔗 URL: ${downloadUrl}`);
      
      const downloadResult = await FileSystem.downloadAsync(downloadUrl, localPath);
      
      if (downloadResult.status === 200) {
        // Verificar o tamanho do arquivo baixado
        const fileInfo = await FileSystem.getInfoAsync(localPath);
        const size = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;
        
        console.log(`✅ Vídeo baixado com sucesso: ${localFileName} (${(size / 1024 / 1024).toFixed(2)} MB)`);
        return true;
      } else {
        console.error(`❌ Erro no download do vídeo: ${localFileName} - Status: ${downloadResult.status}`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Erro ao baixar vídeo: ${videoName}`, error);
      return false;
    }
  }

  public async getLocalVideos(): Promise<string[]> {
    try {
    //  console.log('🔍 Verificando diretório de vídeos:', this.videosDir);
      
      const dirInfo = await FileSystem.getInfoAsync(this.videosDir);
      if (!dirInfo.exists) {
      //  console.log('📂 Diretório de vídeos não existe, criando...');
        await FileSystem.makeDirectoryAsync(this.videosDir, { intermediates: true });
        return [];
      }

      const files = await FileSystem.readDirectoryAsync(this.videosDir);
    //  console.log('📁 Arquivos encontrados no diretório:', files);
      
      const videoFiles = files
        .filter(file => {
          // Aceitar arquivos com extensão de vídeo ou arquivos sem extensão que podem ser vídeos
          const hasVideoExt = file.endsWith('.mp4') || file.endsWith('.mov') || file.endsWith('.avi');
          const hasNoExtension = !file.includes('.');
          const isValid = hasVideoExt || hasNoExtension;
        //  console.log(`📄 Verificando arquivo: ${file} - É vídeo: ${isValid}`);
          return isValid;
        })
        .map(file => this.videosDir + file);
      
    //  console.log('🎬 Vídeos locais encontrados:', videoFiles.length);
      if (videoFiles.length > 0) {
      //  console.log('📝 Lista de vídeos:', videoFiles.map(path => path.split('/').pop()));
      }
      
      return videoFiles;
    } catch (error) {
      console.error('❌ Erro ao buscar vídeos locais:', error);
      return [];
    }
  }

  public async shouldSyncVideos(): Promise<boolean> {
    try {
      const lastSync = await AsyncStorage.getItem('@videos_last_sync');
      
      if (!lastSync) {
        return true; // Primeira sincronização
      }

      const lastSyncDate = new Date(lastSync);
      const now = new Date();
      const midnightToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // Se a última sincronização foi antes da meia-noite de hoje, sincronizar
      return lastSyncDate < midnightToday;
    } catch (error) {
      console.error('❌ Erro ao verificar necessidade de sincronização:', error);
      return true;
    }
  }

  public async setupDailySync(): Promise<void> {
    // Método mantido para compatibilidade, mas agora usa verificação periódica
    console.log('🕛 Configuração de sincronização baseada em verificação periódica (1 minuto)');
    
    // A verificação periódica já foi iniciada no initialize()
    // Este método agora serve apenas para compatibilidade
  }

  private startPeriodicSync(): void {
    // Limpar intervalo anterior se existir
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // Fazer primeira verificação imediatamente
    this.checkAndSyncVideos();

    // Configurar verificação a cada 1 minuto (60000ms)
    this.syncInterval = setInterval(() => {
      this.checkAndSyncVideos();
    }, 60000);

    console.log('🕐 Verificação periódica de vídeos iniciada (1 minuto)');
  }

  private async checkAndSyncVideos(): Promise<void> {
    try {
      console.log('🔍 Verificando mudanças nos vídeos...');
      
      // Buscar vídeos disponíveis na API
      const availableVideos = await this.fetchAvailableVideos();
      const availableVideoNames = availableVideos.map(video => video.nome);
      
      // Buscar vídeos locais
      const localVideos = await this.getLocalVideos();
      const localVideoNames = localVideos.map(path => {
        const fileName = path.split('/').pop() || '';
        // Remover extensão para comparação
        return fileName.includes('.') ? fileName.split('.')[0] : fileName;
      });

      // Verificar se há mudanças
      const hasChanges = this.hasVideoChanges(availableVideoNames, localVideoNames);
      
      if (hasChanges) {
        console.log('🎬 Mudanças detectadas nos vídeos, iniciando sincronização...');
        await this.syncVideos();
      } else {
        console.log('✅ Nenhuma mudança detectada nos vídeos');
      }
    } catch (error) {
      console.error('❌ Erro na verificação periódica de vídeos:', error);
    }
  }

  private hasVideoChanges(availableVideoNames: string[], localVideoNames: string[]): boolean {
    // Normalizar nomes removendo extensões dos disponíveis também
    const normalizedAvailable = availableVideoNames.map(name => 
      name.includes('.') ? name.split('.')[0] : name
    );

    // Verificar se há vídeos novos
    const newVideos = normalizedAvailable.filter(name => !localVideoNames.includes(name));
    
    // Verificar se há vídeos removidos
    const removedVideos = localVideoNames.filter(name => !normalizedAvailable.includes(name));
    
    // Verificar se a quantidade mudou
    const quantityChanged = normalizedAvailable.length !== localVideoNames.length;
    
    if (newVideos.length > 0) {
      console.log('📥 Novos vídeos detectados:', newVideos);
    }
    
    if (removedVideos.length > 0) {
      console.log('🗑️ Vídeos removidos detectados:', removedVideos);
    }
    
    return newVideos.length > 0 || removedVideos.length > 0 || quantityChanged;
  }

  public stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('⏹️ Verificação periódica de vídeos interrompida');
    }
  }

  public getRandomVideo(availableVideos: string[]): string | null {
    if (availableVideos.length === 0) {
      return null;
    }
    
    const randomIndex = Math.floor(Math.random() * availableVideos.length);
    return availableVideos[randomIndex];
  }

  public getCurrentVideoUri(availableVideos: string[], currentIndex: number): string | null {
    if (availableVideos.length === 0 || currentIndex >= availableVideos.length) {
      return null;
    }
    
    const videoUri = availableVideos[currentIndex];

    
    return videoUri;
  }

  public async debugVideoDirectory(): Promise<void> {
    try {
    // //  console.log('🔍 === DEBUG: Informações do diretório de vídeos ===');
    // //  console.log('📂 Diretório:', this.videosDir);
      
      const dirInfo = await FileSystem.getInfoAsync(this.videosDir);
    // //  console.log('📁 Diretório existe:', dirInfo.exists);
      
      if (!dirInfo.exists) {
        //console.log('📂 Criando diretório de vídeos...');
        await FileSystem.makeDirectoryAsync(this.videosDir, { intermediates: true });
        return;
      }

      const files = await FileSystem.readDirectoryAsync(this.videosDir);
     // console.log('📄 Total de arquivos:', files.length);
      
      for (const file of files) {
        const filePath = this.videosDir + file;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        const size = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;
        //console.log(`📄 ${file} - Tamanho: ${size} bytes - Existe: ${fileInfo.exists}`);
      }
      
     // console.log('🔍 === FIM DEBUG ===');
    } catch (error) {
     // console.error('❌ Erro no debug do diretório:', error);
    }
  }

  public async fixVideoExtensions(): Promise<void> {
    try {
      //console.log('🔧 Corrigindo extensões de vídeos...');
      
      const dirInfo = await FileSystem.getInfoAsync(this.videosDir);
      if (!dirInfo.exists) {
        return;
      }

      const files = await FileSystem.readDirectoryAsync(this.videosDir);
      
      for (const file of files) {
        // Se o arquivo não tem extensão e não é um diretório
        if (!file.includes('.')) {
          const oldPath = this.videosDir + file;
          const newPath = this.videosDir + file + '.mp4';
          
          try {
            const fileInfo = await FileSystem.getInfoAsync(oldPath);
            if (fileInfo.exists && !fileInfo.isDirectory) {
              await FileSystem.moveAsync({ from: oldPath, to: newPath });
             // console.log(`🔧 Arquivo renomeado: ${file} → ${file}.mp4`);
            }
          } catch (error) {
           // console.error(`❌ Erro ao renomear ${file}:`, error);
          }
        }
      }
      
     // console.log('✅ Correção de extensões concluída');
    } catch (error) {
    //  console.error('❌ Erro ao corrigir extensões:', error);
    }
  }

  public async forceCheckAndSync(): Promise<void> {
    console.log('🔄 Verificação manual de vídeos solicitada');
    await this.checkAndSyncVideos();
  }
}
