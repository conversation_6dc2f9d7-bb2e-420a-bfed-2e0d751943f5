# Nome do Projeto

Breve descrição do projeto.

## Tabela de Conteúdos

- [Instalação](#instalação)
- [Execução](#execução)
- [Build](#build)
- [Licença](#licença)

## Instalação

### Pré-requisitos

- Node.js (>= 12.x)
- npm (>= 6.x) ou yarn (>= 1.x)
- Expo CLI
- JDK Java 21.0.4 (LTS)

Para instalar as dependências necessárias, execute:

```bash
npm install
```

## Execução

### Dev

Para executar o projeto em modo desenvolvimento, execute:

```bash
npm run start
```

## Build

Para construir o projeto, execute:

```bash

## Para gerar a pasta android 

npm run start
```

Adicione a seguinte tag ao arquivo `AndroidManifest.xml` para tornar a aplicação um launcher:

```bash
<intent-filter>
    <action android:name="android.intent.action.MAIN"/>
    <category android:name="android.intent.category.LAUNCHER" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.HOME" />
</intent-filter>
```

Gerar o apk:

```bash
npm run build
```




