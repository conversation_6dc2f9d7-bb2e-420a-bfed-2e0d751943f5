# Script para otimizar imagens
# Execute este comando no terminal dentro da pasta do projeto:
# npm install -g imagemin-cli imagemin-webp imagemin-pngquant imagemin-mozjpeg

# Para otimizar todas as imagens PNG:
# imagemin assets/images/*.png --out-dir=assets/images/optimized --plugin=pngquant

# Para otimizar todas as imagens JPG:
# imagemin assets/images/*.jpg --out-dir=assets/images/optimized --plugin=mozjpeg

# Para converter para WebP (format mais leve):
# imagemin assets/images/*.{jpg,png} --out-dir=assets/images/webp --plugin=webp

# Comandos específicos para suas imagens:
imagemin assets/images/adaptive-icon.png --out-dir=assets/images --plugin=pngquant --plugin.pngquant.quality=0.6-0.8
imagemin assets/images/icon.png --out-dir=assets/images --plugin=pngquant --plugin.pngquant.quality=0.6-0.8
imagemin assets/images/splash.png --out-dir=assets/images --plugin=pngquant --plugin.pngquant.quality=0.6-0.8
imagemin assets/images/logo-sr.png --out-dir=assets/images --plugin=pngquant --plugin.pngquant.quality=0.6-0.8
imagemin assets/images/favicon.png --out-dir=assets/images --plugin=pngquant --plugin.pngquant.quality=0.6-0.8
