import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import axios from 'axios';

export interface ImageSyncConfig {
  s3BucketUrl: string;
  bucketName: string;
  localImagesDir: string;
}

export class ImageSyncService {
  private static instance: ImageSyncService | null = null;
  private config: ImageSyncConfig;
  private syncIntervalId: NodeJS.Timeout | null = null;
  private isSyncing: boolean = false;
  private syncAbortController: AbortController | null = null;
  private isInitialized: boolean = false;
  private globalProgressCallback: ((progress: { current: number, total: number, ean: string }) => void) | null = null;

  private constructor(config: ImageSyncConfig) {
    //console.log('🚀 Inicializando ImageSyncService com config:', config);
    this.config = config;
    this.isInitialized = true;
    
    //console.log('🚀 ImageSyncService inicializado rapidamente');
    
    // Configura notificações de forma assíncrona (não bloqueia)
    this.setupNotifications().catch(error => {
      //console.log('Aviso: Erro ao configurar notificações:', error);
    });
  }

  // Método estático para obter a instância singleton
  public static getInstance(config?: ImageSyncConfig): ImageSyncService {
    //console.log('📋 getInstance chamado - instance exists:', !!ImageSyncService.instance, 'config provided:', !!config);
    
    if (!ImageSyncService.instance && config) {
     // console.log('🔨 Criando nova instância do ImageSyncService');
      ImageSyncService.instance = new ImageSyncService(config);
    } else if (!ImageSyncService.instance) {
      //console.log('❌ Erro: ImageSyncService precisa ser inicializado com config primeiro');
     // throw new Error('ImageSyncService precisa ser inicializado com config primeiro');
    } else {
      //console.log('✅ Retornando instância existente do ImageSyncService');
    }
    
    const status = ImageSyncService.instance.getServiceStatus();
   // console.log('📊 Status do serviço:', status);
    
    return ImageSyncService.instance;
  }

  // Método para resetar a instância (útil para testes ou reconfiguração)
  public static resetInstance(): void {
    if (ImageSyncService.instance) {
      ImageSyncService.instance.stopDailySync();
      ImageSyncService.instance = null;
    }
  }

  // Atualiza a configuração da instância existente
  public updateConfig(config: ImageSyncConfig): void {
    this.config = config;
  //  console.log('Configuração do ImageSyncService atualizada');
  }

  private async setupNotifications() {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
      //  console.log('Permissão de notificação não concedida');
      }
    } catch (error) {
    //  console.log('Erro ao configurar notificações:', error);
    }
  }

  // Verifica se o serviço está pronto para uso
  public isReady(): boolean {
    return this.isInitialized;
  }

  // Configura o diretório local para armazenar as imagens (com cache otimizado)
  private imageDirectoryCache: string | null = null;
  private directorySetupPromise: Promise<string> | null = null;
  
  private async ensureImageDirectory(): Promise<string> {
    // Usa cache se já foi criado antes
    if (this.imageDirectoryCache) {
      return this.imageDirectoryCache;
    }
    
    // Se já existe uma Promise de setup em andamento, reutiliza ela
    if (this.directorySetupPromise) {
      return this.directorySetupPromise;
    }
    
    // Cria nova Promise de setup
    this.directorySetupPromise = this.setupImageDirectory();
    
    try {
      const result = await this.directorySetupPromise;
      this.imageDirectoryCache = result;
      return result;
    } catch (error) {
      // Reset da Promise em caso de erro para permitir retry
      this.directorySetupPromise = null;
      throw error;
    }
  }
  
  private async setupImageDirectory(): Promise<string> {
    const imageDir = `${FileSystem.documentDirectory}${this.config.localImagesDir}/`;
    
  //  console.log('📁 Configurando diretório de imagens:', imageDir);
    
    try {
      const dirInfo = await FileSystem.getInfoAsync(imageDir);
      if (!dirInfo.exists) {
      //  console.log('📁 Criando diretório de imagens...');
        await FileSystem.makeDirectoryAsync(imageDir, { intermediates: true });
        
        // Verifica se o diretório foi criado com sucesso
        const verifyDirInfo = await FileSystem.getInfoAsync(imageDir);
        if (verifyDirInfo.exists) {
        //  console.log('📁 Diretório de imagens criado com sucesso:', imageDir);
        } else {
        //  console.log('⚠️ Falha na verificação do diretório criado:', imageDir);
        }
      } else {
      //  console.log('📁 Diretório de imagens já existe:', imageDir);
      }
      return imageDir;
    } catch (error) {
    //  console.log('⚠️ Erro ao criar diretório de imagens:', error);
      // Tenta criar o diretório novamente com um caminho mais simples
      try {
        const fallbackDir = `${FileSystem.documentDirectory}produto_images/`;
      //  console.log('📁 Tentando criar diretório de fallback:', fallbackDir);
        await FileSystem.makeDirectoryAsync(fallbackDir, { intermediates: true });
      //  console.log('📁 Diretório de fallback criado:', fallbackDir);
        return fallbackDir;
      } catch (fallbackError) {
      //  console.log('⚠️ Erro também no diretório de fallback:', fallbackError);
        return imageDir; // Retorna o original mesmo com erro
      }
    }
  }

  // Baixa uma imagem específica do S3 com timeout
  public async downloadImageWithTimeout(ean: string, timeoutMs: number = 10000): Promise<string | null> {
    return new Promise(async (resolve) => {
      const timeoutId = setTimeout(() => {
        resolve(null);
      }, timeoutMs);

      try {
        const result = await this.downloadImage(ean);
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        resolve(null);
      }
    });
  }

  // Baixa uma imagem específica do S3
  public async downloadImage(ean: string): Promise<string | null> {
    try {
      const imageDir = await this.ensureImageDirectory();
      
      // Verifica se já existe PNG ou JPG localmente
      const localPngPath = `${imageDir}${ean}.png`;
      const localJpgPath = `${imageDir}${ean}.jpg`;
      
      const pngInfo = await FileSystem.getInfoAsync(localPngPath);
      if (pngInfo.exists) {
        return localPngPath;
      }
      
      const jpgInfo = await FileSystem.getInfoAsync(localJpgPath);
      if (jpgInfo.exists) {
        return localJpgPath;
      }

      // URLs para testar
      const pngUrl = `${this.config.s3BucketUrl}/${this.config.bucketName}/${ean}.png`;
      const jpgUrl = `${this.config.s3BucketUrl}/${this.config.bucketName}/${ean}.jpg`;

      // Tenta baixar PNG primeiro, depois JPG se falhar
      let downloadResult: FileSystem.FileSystemDownloadResult | null = null;
      let localPath: string;
      
      try {
        // Tenta PNG primeiro
        localPath = localPngPath;
        downloadResult = await FileSystem.downloadAsync(pngUrl, localPath);
        
        if (downloadResult.status !== 200) {
          // Se PNG falhou, tenta JPG
          localPath = localJpgPath;
          downloadResult = await FileSystem.downloadAsync(jpgUrl, localPath);
        }
      } catch (error) {
        // Se PNG falhou, tenta JPG
        try {
          localPath = localJpgPath;
          downloadResult = await FileSystem.downloadAsync(jpgUrl, localPath);
        } catch (jpgError) {
        ////  console.log(`Nenhuma imagem encontrada para EAN: ${ean}`);
          return null;
        }
      }
      
      if (downloadResult && downloadResult.status === 200) {
        // Verifica se o arquivo baixado é realmente uma imagem válida
        const fileInfo = await FileSystem.getInfoAsync(localPath);
        if (fileInfo.exists && fileInfo.size && fileInfo.size > 1000) { // Pelo menos 1KB
        //  console.log(`Imagem baixada com sucesso: ${localPath} (${fileInfo.size} bytes)`);
          return localPath;
        } else {
          // Remove arquivo inválido
          await FileSystem.deleteAsync(localPath, { idempotent: true });
        //  console.log(`Arquivo baixado inválido removido: ${localPath}`);
          return null;
        }
      } else {
        //console.log(`Nenhuma imagem encontrada para EAN: ${ean}`);
        return null;
      }
    } catch (error) {
    ////  console.log('Erro ao baixar imagem do produto:', ean, error);
      return null;
    }
  }

  // Sincroniza todas as imagens do S3
  public async syncAllImages(progressCallback?: (progress: { current: number, total: number, ean: string }) => void): Promise<void> {
    // Verifica se já está sincronizando
    if (this.isSyncing) {
    //  console.log('Sincronização já está em andamento');
      return;
    }

    this.isSyncing = true;
    this.syncAbortController = new AbortController();

    try {
    //  console.log('Iniciando sincronização de imagens...');
      
      // Busca a lista de produtos da API
      const apiUrl = await AsyncStorage.getItem('@api_url');
      const filial = await AsyncStorage.getItem('@filial');
      
      if (!apiUrl || !filial) {
      //  console.log('Configurações de API não encontradas');
        return;
      }

      // Busca lista de produtos para obter os EANs
      const response = await axios.get(`${apiUrl}/produtos/eans`, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
        signal: this.syncAbortController.signal
      });

      const eans: string[] = response.data;
    //  console.log(`Encontrados ${eans.length} produtos para sincronizar`);

      const imageDir = await this.ensureImageDirectory();
      let downloadedCount = 0;
      let errorCount = 0;
      let skippedCount = 0;

      // Verifica quais imagens já existem localmente
      let existingFiles: string[] = [];
      try {
        const dirInfo = await FileSystem.getInfoAsync(imageDir);
        if (dirInfo.exists) {
          existingFiles = await FileSystem.readDirectoryAsync(imageDir);
        }
      } catch (error) {
      //  console.log('Aviso: Erro ao ler diretório de imagens existentes:', error);
        existingFiles = [];
      }
      
      const existingEans = new Set();
      existingFiles.forEach(file => {
        const ean = file.replace(/\.(png|jpg)$/, '');
        existingEans.add(ean);
      });

      // Filtra apenas EANs que não possuem imagem local
      const eansToDownload = eans.filter(ean => !existingEans.has(ean));
    //  console.log(`${eans.length - eansToDownload.length} imagens já existem localmente`);
    //  console.log(`Baixando ${eansToDownload.length} novas imagens`);

      // Download com controle de concorrência para evitar sobrecarga
      const concurrencyLimit = 10; // Máximo 10 downloads simultâneos
      const batchSize = 50; // Processa 50 itens por vez
      
      for (let i = 0; i < eansToDownload.length; i += batchSize) {
        if (this.syncAbortController?.signal.aborted) {
        //  console.log('Sincronização cancelada');
          break;
        }

        const batch = eansToDownload.slice(i, i + batchSize);
        
        // Processa o lote com limite de concorrência
        await this.processBatchWithConcurrencyLimit(
          batch, 
          concurrencyLimit, 
          async (ean: string, index: number) => {
            try {
              if (this.syncAbortController?.signal.aborted) return;

              const currentIndex = i + index + 1;
              
              // Callback de progresso
              const progress = {
                current: currentIndex,
                total: eansToDownload.length,
                ean: ean
              };
              
              // Salva progresso no AsyncStorage para compartilhar entre telas
              await this.saveProgressToStorage(progress);
              
              // Chama callback local se fornecido
              if (progressCallback) {
                progressCallback(progress);
              }
              
              // Chama callback global se configurado
              if (this.globalProgressCallback) {
                this.globalProgressCallback(progress);
              }
              
              const localPath = await this.downloadImageWithTimeout(ean, 15000);
              if (localPath) {
                downloadedCount++;
              } else {
                errorCount++;
              }
            } catch (error) {
            //  console.log(`Erro ao baixar imagem ${ean}:`, error);
              errorCount++;
            }
          }
        );
        
        // Pausa entre lotes para não sobrecarregar
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Conta imagens que já existiam
      skippedCount = eans.length - eansToDownload.length;

    //  console.log(`Sincronização concluída: ${downloadedCount} baixadas, ${skippedCount} já existiam, ${errorCount} erros`);
      
      // Limpa progresso do AsyncStorage ao finalizar
      await this.clearProgressFromStorage();
      
      // Limpa cache de imagens locais para garantir dados atualizados
      this.clearImageCache();
      
      // Salva timestamp da última sincronização
      await AsyncStorage.setItem('@last_image_sync', new Date().toISOString());
      
      // Envia notificação de sucesso
      await this.sendSyncNotification(downloadedCount, errorCount, skippedCount);

    } catch (error) {
      if (error.name === 'AbortError') {
      //  console.log('Sincronização cancelada pelo usuário');
      } else {
      //  console.log('Erro na sincronização de imagens:', error);
        await this.sendSyncNotification(0, -1, 0);
      }
      
      // Limpa progresso do AsyncStorage em caso de erro
      await this.clearProgressFromStorage();
    } finally {
      this.isSyncing = false;
      this.syncAbortController = null;
      // Sempre limpa o cache ao final da sincronização
      this.clearImageCache();
    }
  }

  // Processa um lote de itens com limite de concorrência
  private async processBatchWithConcurrencyLimit<T>(
    items: T[],
    concurrencyLimit: number,
    processor: (item: T, index: number) => Promise<void>
  ): Promise<void> {
    const semaphore = new Array(concurrencyLimit).fill(null);
    let index = 0;

    const workers = semaphore.map(async () => {
      while (index < items.length) {
        const currentIndex = index++;
        const item = items[currentIndex];
        
        if (this.syncAbortController?.signal.aborted) {
          break;
        }
        
        try {
          await processor(item, currentIndex);
        } catch (error) {
        //  console.log(`Erro ao processar item ${currentIndex}:`, error);
        }
      }
    });

    await Promise.all(workers);
  }

  // Limpa imagens antigas que não estão mais na lista de produtos
  public async cleanupOldImages(): Promise<void> {
    try {
      const imageDir = await this.ensureImageDirectory();
      
      // Verifica se o diretório existe antes de tentar ler
      const dirInfo = await FileSystem.getInfoAsync(imageDir);
      if (!dirInfo.exists) {
      //  console.log('Diretório de imagens não existe, nada para limpar');
        return;
      }
      
      const files = await FileSystem.readDirectoryAsync(imageDir);
      
      // Busca lista atual de EANs
      const apiUrl = await AsyncStorage.getItem('@api_url');
      if (!apiUrl) return;

      const response = await axios.get(`${apiUrl}/produtos/eans`);
      const currentEans: string[] = response.data;
      const currentEansSet = new Set();
      
      // Adiciona tanto PNG quanto JPG para cada EAN
      currentEans.forEach(ean => {
        currentEansSet.add(`${ean}.png`);
        currentEansSet.add(`${ean}.jpg`);
      });

      // Remove arquivos que não estão na lista atual ou são inválidos
      for (const file of files) {
        const filePath = `${imageDir}${file}`;
        
        try {
          // Verifica se o arquivo não está na lista atual
          if (!currentEansSet.has(file)) {
            await FileSystem.deleteAsync(filePath);
          //  console.log('Imagem removida (não está na lista):', file);
            continue;
          }

          // Verifica se é uma imagem válida
          const fileInfo = await FileSystem.getInfoAsync(filePath);
          if (fileInfo.exists) {
            // Remove arquivos muito pequenos (provavelmente erros XML)
            if (fileInfo.size && fileInfo.size < 1000) {
              await FileSystem.deleteAsync(filePath);
            //  console.log(`Arquivo inválido removido (${fileInfo.size} bytes):`, file);
              continue;
            }

            // Verifica se o arquivo tem extensão de imagem
            if (!file.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
              await FileSystem.deleteAsync(filePath);
            //  console.log('Arquivo não é imagem removido:', file);
              continue;
            }
          }
        } catch (error) {
        //  console.log(`Erro ao verificar arquivo ${file}:`, error);
        }
      }

    //  console.log('Limpeza de imagens concluída');
    } catch (error) {
    //  console.log('Erro ao limpar imagens antigas:', error);
    }
  }

  // Busca imagem local de um produto
  public async getLocalImagePath(ean: string): Promise<string | null> {
    try {
      const imageDir = await this.ensureImageDirectory();
      
      // Verifica PNG primeiro
      const localPngPath = `${imageDir}${ean}.png`;
      const pngInfo = await FileSystem.getInfoAsync(localPngPath);
      if (pngInfo.exists) {
        return localPngPath;
      }
      
      // Depois verifica JPG
      const localJpgPath = `${imageDir}${ean}.jpg`;
      const jpgInfo = await FileSystem.getInfoAsync(localJpgPath);
      if (jpgInfo.exists) {
        return localJpgPath;
      }
      
      // Se não existe, tenta baixar
      return await this.downloadImage(ean);
    } catch (error) {
    //  console.log('Erro ao buscar imagem local:', error);
      return null;
    }
  }

  // Cache para arquivos locais (melhora performance)
  private localImageCache: Map<string, string | null> = new Map();
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 60000; // 1 minuto

  // Verifica se existe imagem local sem fazer download (otimizado com cache)
  public async checkLocalImageExists(ean: string): Promise<string | null> {
    if (!this.isInitialized) {
    //  console.log('⚠️ ImageSyncService não inicializado para EAN:', ean);
      return null;
    }

    // Verifica cache primeiro para máxima velocidade
    const now = Date.now();
    if (now < this.cacheExpiry && this.localImageCache.has(ean)) {
      const cachedPath = this.localImageCache.get(ean);
      if (cachedPath) {
      //  console.log('📱 Imagem encontrada no cache:', cachedPath);
        return cachedPath;
      }
    }

    try {
      const imageDir = await this.ensureImageDirectory();
      
      // Verifica PNG primeiro
      const localPngPath = `${imageDir}${ean}.png`;
      try {
        const pngInfo = await FileSystem.getInfoAsync(localPngPath);
        if (pngInfo.exists && pngInfo.size && pngInfo.size > 1000) {
          // Atualiza cache
          this.localImageCache.set(ean, localPngPath);
          this.cacheExpiry = now + this.CACHE_DURATION;
        //  console.log('✅ Imagem PNG local encontrada:', localPngPath);
          return localPngPath;
        }
      } catch (error) {
        // Ignora erro, tenta JPG
      //  console.log('Erro ao verificar PNG:', error);
      }
      
      // Depois verifica JPG
      const localJpgPath = `${imageDir}${ean}.jpg`;
      try {
        const jpgInfo = await FileSystem.getInfoAsync(localJpgPath);
        if (jpgInfo.exists && jpgInfo.size && jpgInfo.size > 1000) {
          // Atualiza cache
          this.localImageCache.set(ean, localJpgPath);
          this.cacheExpiry = now + this.CACHE_DURATION;
        //  console.log('✅ Imagem JPG local encontrada:', localJpgPath);
          return localJpgPath;
        }
      } catch (error) {
        // Ignora erro
      //  console.log('Erro ao verificar JPG:', error);
      }
      
      // Cache resultado negativo também
      this.localImageCache.set(ean, null);
      this.cacheExpiry = now + this.CACHE_DURATION;
    //  console.log('❌ Nenhuma imagem local encontrada para EAN:', ean);
      return null;
    } catch (error) {
    //  console.log('❌ Erro ao verificar imagem local para EAN:', ean, error);
      return null;
    }
  }

  // Cache para evitar verificações repetitivas de imagens
  private imageExistsCache = new Map<string, { exists: boolean, path: string | null, timestamp: number }>();
  private readonly IMAGE_CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

  // Verifica se uma imagem existe localmente (versão otimizada com cache)
  public async checkImageExists(ean: string): Promise<string | null> {
    // Verifica cache primeiro
    const cached = this.imageExistsCache.get(ean);
    const now = Date.now();
    
    if (cached && (now - cached.timestamp) < this.IMAGE_CACHE_DURATION) {
      return cached.exists ? cached.path : null;
    }

    try {
      const imageDir = await this.ensureImageDirectory();
      
      // Verifica PNG e JPG em paralelo
      const pngPath = `${imageDir}${ean}.png`;
      const jpgPath = `${imageDir}${ean}.jpg`;
      
      const [pngInfo, jpgInfo] = await Promise.all([
        FileSystem.getInfoAsync(pngPath),
        FileSystem.getInfoAsync(jpgPath)
      ]);
      
      let resultPath: string | null = null;
      
      // Prioriza PNG se ambos existirem e forem válidos
      if (pngInfo.exists && pngInfo.size && pngInfo.size > 1000) {
        resultPath = pngPath;
      } else if (jpgInfo.exists && jpgInfo.size && jpgInfo.size > 1000) {
        resultPath = jpgPath;
      }
      
      // Atualiza cache
      this.imageExistsCache.set(ean, {
        exists: resultPath !== null,
        path: resultPath,
        timestamp: now
      });
      
      return resultPath;
    } catch (error) {
    //  console.log('Erro ao verificar imagem local:', error);
      // Cache resultado negativo por pouco tempo
      this.imageExistsCache.set(ean, {
        exists: false,
        path: null,
        timestamp: now
      });
      return null;
    }
  }

  // Limpa cache de imagens (útil após sincronização)
  public clearImageCache(): void {
    this.imageExistsCache.clear();
  //  console.log('Cache de imagens limpo');
  }

  // Configura sincronização automática à meia-noite
  public setupDailySync(): void {
    // Cancela sincronização anterior se existir
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
    }

    // Calcula o tempo até a próxima meia-noite
    const now = new Date();
    const nextMidnight = new Date();
    nextMidnight.setDate(now.getDate() + 1);
    nextMidnight.setHours(0, 0, 0, 0);
    
    const timeUntilMidnight = nextMidnight.getTime() - now.getTime();

  //  console.log(`Próxima sincronização em ${Math.round(timeUntilMidnight / 1000 / 60)} minutos`);

    // Agenda a primeira sincronização
    setTimeout(() => {
      this.syncAllImages();
      
      // Configura sincronização diária (24 horas = 86400000 ms)
      this.syncIntervalId = setInterval(() => {
        this.syncAllImages();
      }, 24 * 60 * 60 * 1000);
      
    }, timeUntilMidnight);
  }

  // Para a sincronização automática
  public stopDailySync(): void {
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
      this.syncIntervalId = null;
    //  console.log('Sincronização automática interrompida');
    }
  }

  // Envia notificação sobre o status da sincronização
  private async sendSyncNotification(downloaded: number, errors: number, skipped: number = 0): Promise<void> {
    try {
      let title = 'Sincronização de Imagens';
      let body = '';
      
      if (errors === -1) {
        body = 'Erro na sincronização de imagens';
      } else if (errors === 0) {
        if (skipped > 0) {
          body = `${downloaded} novas imagens baixadas, ${skipped} já existiam`;
        } else {
          body = `${downloaded} imagens sincronizadas com sucesso`;
        }
      } else {
        body = `${downloaded} imagens baixadas, ${skipped} já existiam, ${errors} erros`;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
        },
        trigger: null,
      });
    } catch (error) {
    //  console.log('Erro ao enviar notificação:', error);
    }
  }

  // Verifica se precisa sincronizar (última sincronização foi há mais de 7 dias)
  public async shouldSync(): Promise<boolean> {
    try {
      const lastSync = await AsyncStorage.getItem('@last_image_sync');
      if (!lastSync) return true;

      const lastSyncDate = new Date(lastSync);
      const now = new Date();
      const diffDays = (now.getTime() - lastSyncDate.getTime()) / (1000 * 60 * 60 * 24);
      
      // Sincroniza apenas se passou mais de 7 dias
      return diffDays >= 7;
    } catch (error) {
    //  console.log('Erro ao verificar última sincronização:', error);
      return true;
    }
  }

  // Força sincronização manual
  public async forceSyncImages(progressCallback?: (progress: { current: number, total: number, ean: string }) => void): Promise<void> {
  //  console.log('Iniciando sincronização manual...');
    await this.syncAllImages(progressCallback);
    await this.cleanupOldImages();
  }

  // Cancela sincronização em andamento
  public cancelSync(): void {
    if (this.syncAbortController) {
      this.syncAbortController.abort();
    //  console.log('Cancelamento de sincronização solicitado');
    }
  }

  // Verifica se está sincronizando
  public isSyncInProgress(): boolean {
    return this.isSyncing;
  }

  // Sincronização em background (sem bloquear a UI)
  public async backgroundSync(): Promise<void> {
    if (this.isSyncing) {
    //  console.log('Sincronização já em andamento, ignorando solicitação de background sync');
      return;
    }

    // Verifica se realmente precisa sincronizar
    const shouldSyncNow = await this.shouldSync();
    if (!shouldSyncNow) {
    //  console.log('Sincronização não necessária no momento');
      return;
    }

  //  console.log('Iniciando sincronização em background...');
    
    // Executa sincronização com callback para salvar progresso no AsyncStorage
    const progressCallback = async (progress: { current: number, total: number, ean: string }) => {
      await this.saveProgressToStorage(progress);
      
      // Também chama callback global se configurado
      if (this.globalProgressCallback) {
        this.globalProgressCallback(progress);
      }
    };
    
    // Executa sincronização sem aguardar o resultado
    this.syncAllImages(progressCallback).catch(error => {
    //  console.log('Erro na sincronização em background:', error);
    });
  }

  // Obtém estatísticas de armazenamento
  public async getStorageStats(): Promise<{totalImages: number, totalSize: number}> {
    try {
      const imageDir = await this.ensureImageDirectory();
      
      // Verifica se o diretório existe antes de tentar ler
      const dirInfo = await FileSystem.getInfoAsync(imageDir);
      if (!dirInfo.exists) {
      //  console.log('Diretório de imagens não existe, retornando estatísticas vazias');
        return { totalImages: 0, totalSize: 0 };
      }
      
      const files = await FileSystem.readDirectoryAsync(imageDir);
      
      let totalSize = 0;
      let validImages = 0;
      for (const file of files) {
        const filePath = `${imageDir}${file}`;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        if (fileInfo.exists && !fileInfo.isDirectory && (file.endsWith('.jpg') || file.endsWith('.png'))) {
          totalSize += fileInfo.size || 0;
          validImages++;
        }
      }

      return {
        totalImages: validImages,
        totalSize
      };
    } catch (error) {
    //  console.log('Erro ao obter estatísticas:', error);
      return { totalImages: 0, totalSize: 0 };
    }
  }

  // Método de debug para listar arquivos no diretório de imagens
  public async debugListFiles(): Promise<void> {
    try {
      const imageDir = await this.ensureImageDirectory();
      
      // Verifica se o diretório existe antes de tentar ler
      const dirInfo = await FileSystem.getInfoAsync(imageDir);
      if (!dirInfo.exists) {
      //  console.log('=== DEBUG: Diretório de imagens não existe ===');
      //  console.log('Diretório:', imageDir);
      //  console.log('=== FIM DEBUG ===');
        return;
      }
      
      const files = await FileSystem.readDirectoryAsync(imageDir);
      
    //  console.log('=== DEBUG: Arquivos no diretório de imagens ===');
    //  console.log('Diretório:', imageDir);
    //  console.log('Total de arquivos:', files.length);
      
      for (const file of files) {
        const filePath = `${imageDir}${file}`;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        const size = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;
      //  console.log(`- ${file} (${size} bytes, existe: ${fileInfo.exists})`);
      }
    //  console.log('=== FIM DEBUG ===');
    } catch (error) {
    //  console.log('Erro no debug de arquivos:', error);
    }
  }

  // Método para definir callback global de progresso
  public setGlobalProgressCallback(callback: (progress: { current: number, total: number, ean: string }) => void): void {
    this.globalProgressCallback = callback;
  }

  // Método para remover callback global de progresso
  public removeGlobalProgressCallback(): void {
    this.globalProgressCallback = null;
  }

  // Salva progresso no AsyncStorage para compartilhar entre telas
  private async saveProgressToStorage(progress: { current: number, total: number, ean: string }): Promise<void> {
    try {
      await AsyncStorage.setItem('@sync_progress', JSON.stringify(progress));
    } catch (error) {
    //  console.log('Erro ao salvar progresso:', error);
    }
  }

  // Carrega progresso do AsyncStorage
  public async getProgressFromStorage(): Promise<{ current: number, total: number, ean: string }> {
    try {
      const progressStr = await AsyncStorage.getItem('@sync_progress');
      if (progressStr) {
        return JSON.parse(progressStr);
      }
    } catch (error) {
    //  console.log('Erro ao carregar progresso:', error);
    }
    return { current: 0, total: 0, ean: '' };
  }

  // Limpa progresso do AsyncStorage
  private async clearProgressFromStorage(): Promise<void> {
    try {
      await AsyncStorage.removeItem('@sync_progress');
    } catch (error) {
    //  console.log('Erro ao limpar progresso:', error);
    }
  }

  // Método para obter status detalhado do serviço (para debug)
  public getServiceStatus(): {
    isInitialized: boolean;
    hasConfig: boolean;
    config: ImageSyncConfig | null;
    isSyncing: boolean;
    cacheSize: number;
  } {
    return {
      isInitialized: this.isInitialized,
      hasConfig: !!this.config,
      config: this.config || null,
      isSyncing: this.isSyncing,
      cacheSize: this.localImageCache.size
    };
  }

  // Método utilitário para garantir que um diretório existe antes de operações
  private async safeEnsureDirectory(dirPath: string): Promise<boolean> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(dirPath);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(dirPath, { intermediates: true });
        // Verifica se foi criado com sucesso
        const verifyInfo = await FileSystem.getInfoAsync(dirPath);
        return verifyInfo.exists;
      }
      return true;
    } catch (error) {
    //  console.log('Erro ao verificar/criar diretório:', dirPath, error);
      return false;
    }
  }
}
