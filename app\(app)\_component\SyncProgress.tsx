import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';

interface SyncProgressProps {
  progress: {
    current: number;
    total: number;
    ean: string;
  };
  onCancel?: () => void;
  showCancel?: boolean;
}

export default function SyncProgress({ progress, onCancel, showCancel = false }: SyncProgressProps) {
  const percentage = progress.total > 0 ? Math.round((progress.current / progress.total) * 100) : 0;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Ionicons name="cloud-download-outline" size={64} color="#D83235" />
        
        <Text style={styles.title}>Sincronizando Imagens</Text>
        <Text style={styles.subtitle}>
          {progress.current} de {progress.total} produtos
        </Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${percentage}%` }
              ]} 
            />
          </View>
          <Text style={styles.percentage}>{percentage}%</Text>
        </View>
        
        {progress.ean && (
          <Text style={styles.currentItem}>
            Processando: {progress.ean}
          </Text>
        )}
        
        <ActivityIndicator size="large" color="#D83235" style={styles.spinner} />
        
        <Text style={styles.message}>
          A sincronização está sendo executada em segundo plano.{'\n'}
          Você pode continuar usando o app normalmente.
        </Text>
        
        {showCancel && onCancel && (
          <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
            <Text style={styles.cancelText}>Cancelar Sincronização</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    width: '90%',
    maxWidth: 400,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#D83235',
    borderRadius: 4,
  },
  percentage: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#D83235',
  },
  currentItem: {
    fontSize: 12,
    color: '#999',
    marginBottom: 20,
    textAlign: 'center',
  },
  spinner: {
    marginBottom: 20,
  },
  message: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  cancelButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  cancelText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
