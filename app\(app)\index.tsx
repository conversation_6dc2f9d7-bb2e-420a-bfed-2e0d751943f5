import { useState, useEffect, useRef, useCallback } from 'react';
import { Text, TextInput, View, StyleSheet, TouchableOpacity, Keyboard, BackHandler, Alert, Animated, Easing } from 'react-native';
import { Image } from 'expo-image';
import { Audio } from 'expo-av';
import { router } from 'expo-router';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons, Entypo } from '@expo/vector-icons';
import * as Network from 'expo-network';
import * as NavigationBar from 'expo-navigation-bar';
import * as FileSystem from 'expo-file-system';
import { ImageSyncService } from '../../services/ImageSyncService';
import VideoPlayer from './_component/VideoPlayer';
import { VideoSyncService } from '../../services/VideoSyncService';

//Components
import ErrorPage from './_component/ErrorPage';
import ProdutoPreco from './_component/ProdutoPreco';


interface IProd {
  CODPROD: number;
  DESCRICAO: string;
  PVENDA: number;
  PVENDAATAC: number;
  DIRFOTOPROD: string;
  EMBALAGEM: string;
}

export default function Page() {
  const [codProd, setCodProd] = useState('');
  const [produto, setProduto] = useState<IProd | null>(null);
  const [showProdPreco, setShowProdPreco] = useState(false);
  const [showError, setShowError] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [produtoImagem, setProdutoImagem] = useState('');
  const inputRef = useRef<TextInput>(null);
  const [sound, setSound] = useState<any>(null);
  const [images, setImages] = useState<string[]>([]);
  const [apiUrl, setApiUrl] = useState('');
  const [filial, setFilial] = useState('');
  const [s3Endpoint, setS3Endpoint] = useState(''); // Novo estado para o endpoint do S3
  const [isSettingsLoaded, setIsSettingsLoaded] = useState(false);
  const [isImageServiceReady, setIsImageServiceReady] = useState(false);
  const [textIndex, setTextIndex] = useState(0);
  const [isSyncInProgress, setIsSyncInProgress] = useState(false);
  const [syncProgress, setSyncProgress] = useState({ current: 0, total: 0, ean: '' });
  const texts = ["SEJA BEM-VINDO", "CONSULTE O PREÇO ABAIXO"];
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  
  // Estados para vídeos promocionaisF
  const [videoSyncService, setVideoSyncService] = useState<VideoSyncService | null>(null);
  const [availableVideos, setAvailableVideos] = useState<string[]>([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [showVideo, setShowVideo] = useState(false);
  const [videoIntervalId, setVideoIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [videoEndTimeout, setVideoEndTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Ref para manter a referência atual dos vídeos disponíveis
  const availableVideosRef = useRef<string[]>([]);

  // Configurações do MinIO S3 - carregadas do AsyncStorage
  const [imageSyncService, setImageSyncService] = useState<ImageSyncService | null>(null);

  // Cache para verificação de conectividade
  const networkCacheRef = useRef<{ isConnected: boolean; timestamp: number } | null>(null);
  const NETWORK_CACHE_DURATION = 5000; // 5 segundos

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(translateY, {
          toValue: 10, // valor para o qual você deseja animar
          duration: 1000, // duração da animação para cima
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 0, // volta para a posição original
          duration: 1000, // duração da animação para baixo
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();
  }, [translateY]);

  useEffect(() => {
      const interval = setInterval(() => {
          // Fade out animation
          Animated.timing(opacity, {
              toValue: 0,
              duration: 300,
              easing: Easing.linear,
              useNativeDriver: true
          }).start(() => {
              // Change text index
              setTextIndex((textIndex + 1) % texts.length);
              // Fade in animation
              Animated.timing(opacity, {
                  toValue: 1,
                  duration: 300,
                  easing: Easing.linear,
                  useNativeDriver: true
              }).start();
          });
      }, 7000); // Change text every 3 seconds

      return () => clearInterval(interval);
  }, [textIndex, opacity]);

  useEffect(() => {
    const backAction = () => {
      /* Alert.alert("Atenção!", "Tem certeza que deseja sair?", [
        {
          text: "Cancel",
          onPress: () => null,
          style: "cancel"
        },
        { text: "YES", onPress: () => BackHandler.exitApp() }
      ]); */
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, []);

  useEffect(() => {

    const unsubscribe = Keyboard.addListener('keyboardDidShow', () => {
      Keyboard.dismiss();
    });

    const loadSettings = async () => {
      try {
        // Debug AsyncStorage first
        await debugAsyncStorage();
        
        const storedApiUrl = await AsyncStorage.getItem('@api_url');
        const storedFilial = await AsyncStorage.getItem('@filial');
        const storedS3Endpoint = await AsyncStorage.getItem('@s3_endpoint');
        const storedS3BucketName = await AsyncStorage.getItem('@s3_bucket_name');

        if (storedApiUrl === null || storedFilial === null) {
          router.replace('/config');
          return;
        }

        setApiUrl(storedApiUrl);
        setFilial(storedFilial);
        setS3Endpoint(storedS3Endpoint || ''); // Salva o endpoint do S3
        setIsSettingsLoaded(true);

        // Inicializa o serviço de sincronização de imagens se as configurações do S3 existirem
        if (validateS3Config(storedS3Endpoint, storedS3BucketName)) {
          try {
          ////  console.log('🚀 Iniciando configuração do ImageSyncService...');
            
            const s3Config = {
              s3BucketUrl: storedS3Endpoint!,
              bucketName: storedS3BucketName!,
              localImagesDir: 'produto_images'
            };
            
          ////  console.log('📋 Configuração S3 preparada:', s3Config);

            // Usa singleton para evitar múltiplas instâncias
            let syncService: ImageSyncService;
            try {
              // Tenta obter instância existente ou criar uma nova
              syncService = ImageSyncService.getInstance(s3Config);
            //  console.log('✅ ImageSyncService singleton obtido com sucesso');
            } catch (error) {
            //  console.log('⚠️ Erro ao obter singleton, resetando e criando nova instância:', error);
              // Se houver erro, reseta e cria nova instância
              ImageSyncService.resetInstance();
              syncService = ImageSyncService.getInstance(s3Config);
            //  console.log('✅ ImageSyncService criado após reset');
            }
            
            // Define o serviço imediatamente para estar disponível
            setImageSyncService(syncService);
            setIsImageServiceReady(true);
            
          //  console.log('📋 ImageSyncService definido no estado React');
            
            // Verifica o status do serviço
            try {
              const status = syncService.getServiceStatus();
            //  console.log('📊 Status do serviço após definição:', status);
            } catch (error) {
            //  console.log('⚠️ Erro ao obter status do serviço:', error);
            }

            // Configura callback global para reportar progresso
            syncService.setGlobalProgressCallback((progress) => {
              setSyncProgress(progress);
              setIsSyncInProgress(progress.total > 0);
            });

            // Executa configurações em background de forma não-bloqueante
            setTimeout(() => {
              try {
                if (!syncService.isSyncInProgress()) {
                //  console.log('🔧 Configurando sincronização diária...');
                  syncService.setupDailySync();
                  
                  // Verifica se precisa sincronizar (em background)
                  syncService.shouldSync().then(shouldSync => {
                    if (shouldSync) {
                    //  console.log('📥 Sincronização necessária, iniciando em background...');
                      setIsSyncInProgress(true);
                      
                      syncService.backgroundSync().finally(() => {
                        setIsSyncInProgress(false);
                        setSyncProgress({ current: 0, total: 0, ean: '' });
                      });
                    } else {
                    //  console.log('✅ Sincronização não necessária');
                    }
                  }).catch(error => {
                  //  console.log('❌ Erro ao verificar necessidade de sincronização:', error);
                  });
                } else {
                //  console.log('⚠️ Sincronização já em andamento, pulando...');
                }
              } catch (error) {
              //  console.log('❌ Erro durante configuração em background:', error);
              }
            }, 1000); // Executa depois de 1 segundo para não afetar a UI inicial
          } catch (error) {
          //  console.log('❌ Erro crítico na inicialização do ImageSyncService:', error);
            setIsImageServiceReady(true); // Marca como pronto mesmo com erro
          }
        } else {
        //  console.log('⚠️ Configurações do S3 não encontradas. ImageSyncService não será inicializado.');
        //  console.log('  - S3 Endpoint presente:', !!storedS3Endpoint);
        //  console.log('  - S3 Bucket Name presente:', !!storedS3BucketName);
          setIsImageServiceReady(true); // Marca como pronto mesmo sem S3
        }

        if (storedApiUrl && storedFilial) {
          fetchImages(storedApiUrl);
          // Inicializar serviço de vídeos promocionais
          initializeVideoService(storedApiUrl);
        }

      } catch (error) {
      //  console.log('Erro ao carregar as configurações', error);
        setIsSettingsLoaded(true);
        setIsImageServiceReady(true);
      }
    };

    /* const intervalInput = setInterval(() => {
      setCodProd('');
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 6000); */

    const intervalSendDispositivoAlive = setInterval(() => {
      if (apiUrl && filial) {
        sendDispositivoAlive(); 
        fetchImages(apiUrl);
      }
    }, 10 * 60 * 1000); // 10 minutos em milissegundos

    loadSettings();

    return () => {
      clearInterval(intervalSendDispositivoAlive);
      /* clearInterval(intervalInput); */
      unsubscribe.remove();
      
      // Para a sincronização ao desmontar o componente
      if (imageSyncService) {
        imageSyncService.stopDailySync();
        imageSyncService.removeGlobalProgressCallback();
      }

      // Limpar intervalo de vídeos e timeouts
      if (videoIntervalId) {
        clearInterval(videoIntervalId);
      }
      if (videoEndTimeout) {
        clearTimeout(videoEndTimeout);
      }
    };
  }, []);

  // Atualizar ref sempre que availableVideos mudar
  useEffect(() => {
    availableVideosRef.current = availableVideos;
  }, [availableVideos]);

  useEffect(() => {
    const imageInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex === images.length - 1 ? 0 : prevIndex + 1));
    }, 12000);

    return () => clearInterval(imageInterval);
  }, [images]);

  /* useEffect(() => {
    if (codProd.length >= 13) {
      getProdPreco();

      setTimeout(() => {
        setCodProd('');
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 3000);
    }
  }, [codProd]); */

 const handleSubmit = () => {
    // Pesquisa somente quando Enter for pressionado
    if (codProd && isSettingsLoaded && apiUrl && filial) {
      getProdPreco(codProd);
    }
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleChangeText = (text: string) => {
    setCodProd(text);
  };

  // Função otimizada para verificar conectividade com cache
  const checkNetworkConnectivity = async (): Promise<boolean> => {
    const now = Date.now();
    
    // Verifica se temos um cache válido
    if (networkCacheRef.current && (now - networkCacheRef.current.timestamp) < NETWORK_CACHE_DURATION) {
      return networkCacheRef.current.isConnected;
    }
    
    // Busca novo estado da rede
    try {
      const networkState = await Network.getNetworkStateAsync();
      const isConnected = networkState.isConnected || false;
      
      // Atualiza o cache
      networkCacheRef.current = {
        isConnected,
        timestamp: now
      };
      
      return isConnected;
    } catch (error) {
      // Em caso de erro, assume que está desconectado
      networkCacheRef.current = {
        isConnected: false,
        timestamp: now
      };
      return false;
    }
  };

  const getProdPreco = async (codProd: string) => {    
    try {
      // Interromper vídeo se estiver sendo exibido
      if (showVideo) {
        stopVideoRotation();
        clearVideoStates();
      }

      // Validate required settings before making request
      if (!apiUrl || !filial) {
      //  console.log('Erro: Configurações não carregadas (apiUrl ou filial ausentes)');
        setShowError(true);
        setCodProd('');
        setTimeout(() => setShowError(false), 3000);
        return;
      }

      // Check network connectivity first (com cache)
      const isConnected = await checkNetworkConnectivity();
      if (!isConnected) {
      //  console.log('Erro: Dispositivo sem conexão de rede');
        setShowError(true);
        setCodProd('');
        setTimeout(() => setShowError(false), 3000);
        return;
      }

      // Executar som em paralelo (não aguardar)
      playSound().catch(error => {
        // Ignora erros de som para não impactar a performance
      });
      
      const urlDinamica = `${apiUrl}/preco/${codProd}/${filial}`;
    //  console.log('Fazendo requisição para:', urlDinamica);
      
      // PRIORIDADE: Busca o preço primeiro - sem aguardar imagens
      const res = await axios.get(urlDinamica, { 
        timeout: 3000, // Reduzido de 5000ms para 3000ms
        headers: {
          'Content-Type': 'application/json',
        }
      });
      const data = res.data;
      
      // Verifica se os dados são válidos
      if (!data || (Array.isArray(data) && data.length === 0) || (data.PVENDA !== undefined && data.PVENDA === 0)) {
      //  console.log('Produto não encontrado');
        setShowError(true);
        setCodProd('');
        setTimeout(() => setShowError(false), 3000);
        return;
      }

      // Busca de imagem otimizada - paralela com processamento dos dados
      let finalImagePath = `${apiUrl}/images/${codProd}.jpg`;
      
      // Função para buscar imagem de forma otimizada
      const getOptimizedImage = async (): Promise<string> => {
        console.log('🔍 Iniciando busca de imagem para produto:', codProd);
        
        if (imageSyncService && isImageServiceReady && imageSyncService.isReady()) {
          console.log('✅ ImageSyncService disponível, tentando busca local...');
          try {
            // Timeout reduzido de 2000ms para 500ms para busca local
            const timeoutPromise = new Promise<string | null>((resolve) => {
              setTimeout(() => {
                console.log('⏰ Timeout na busca local de imagem');
                resolve(null);
              }, 500);
            });
            
            const imagePromise = imageSyncService.checkLocalImageExists(codProd);
            const localPath = await Promise.race([imagePromise, timeoutPromise]);
            
            if (localPath) {
              console.log('✅ Imagem encontrada LOCALMENTE:', localPath);
              return localPath;
            } else {
              console.log('❌ Imagem não encontrada localmente, buscando no servidor...');
              // Busca no servidor em background
              const serverUrl = s3Endpoint || apiUrl;
              const imageUrl = await fetchImages(serverUrl, codProd);
              if (imageUrl) {
                console.log('✅ Imagem encontrada no SERVIDOR:', imageUrl);
              } else {
                console.log('❌ Imagem não encontrada no servidor, usando fallback');
              }
              return imageUrl || finalImagePath;
            }
          } catch (error) {
            console.log('❌ Erro ao verificar imagem local:', error);
            // Em caso de erro, busca no servidor
            const serverUrl = s3Endpoint || apiUrl;
            console.log('🔄 Tentando busca no servidor após erro local...');
            const imageUrl = await fetchImages(serverUrl, codProd);
            if (imageUrl) {
              console.log('✅ Imagem encontrada no SERVIDOR (após erro local):', imageUrl);
            } else {
              console.log('❌ Imagem não encontrada no servidor, usando fallback');
            }
            return imageUrl || finalImagePath;
          }
        } else {
          console.log('⚠️ ImageSyncService não disponível, buscando diretamente no servidor...');
          // Busca no servidor quando service não está disponível
          const serverUrl = s3Endpoint || apiUrl;
          const imageUrl = await fetchImages(serverUrl, codProd);
          if (imageUrl) {
            console.log('✅ Imagem encontrada no SERVIDOR (sem service local):', imageUrl);
          } else {
            console.log('❌ Imagem não encontrada no servidor, usando fallback');
          }
          return imageUrl || finalImagePath;
        }
      };

      // Executa busca de imagem e processamento de dados em paralelo
      const [optimizedImagePath, processedProduct] = await Promise.all([
        getOptimizedImage(),
        Promise.resolve((() => {
          // Processa os dados do produto
          if (Array.isArray(data) && data.length > 0) {
            // Formato de array de arrays
            return {
              CODPROD: data[0][0],
              DESCRICAO: data[0][1],
              PVENDA: data[0][2],
              PVENDAATAC: data[0][3],
              DIRFOTOPROD: data[0][4],
              EMBALAGEM: data[0][5],
            };
          } else if (data && typeof data === 'object' && data.CODPROD) {
            // Formato de objeto único
            return {
              CODPROD: data.CODPROD,
              DESCRICAO: data.DESCRICAO,
              PVENDA: data.PVENDA,
              PVENDAATAC: data.PVENDAATAC,
              DIRFOTOPROD: data.DIRFOTOPROD,
              EMBALAGEM: data.EMBALAGEM,
            };
          }
          return null;
        })())
      ]);

      if (!processedProduct) {
      //  console.log('Produto não encontrado - formato de dados inválido');
        setShowError(true);
        setCodProd('');
        setTimeout(() => setShowError(false), 3000);
        return;
      }

      // Atualiza estados com os dados processados
      console.log('📸 Imagem final utilizada para o produto:', optimizedImagePath);
      setProdutoImagem(optimizedImagePath);
      setProduto(processedProduct);
      setShowProdPreco(true);
      setCodProd('');
      setTimeout(() => setShowProdPreco(false), 3000);

      // Processa os dados do produto
      if (Array.isArray(data) && data.length > 0) {
        // Formato de array de arrays
        const prod: IProd = {
          CODPROD: data[0][0],
          DESCRICAO: data[0][1],
          PVENDA: data[0][2],
          PVENDAATAC: data[0][3],
          DIRFOTOPROD: data[0][4],
          EMBALAGEM: data[0][5],
        };

        setProduto(prod);
        setShowProdPreco(true);
        setCodProd('');
        setTimeout(() => setShowProdPreco(false), 3000);
      } else if (data && typeof data === 'object' && data.CODPROD) {
        // Formato de objeto único
        const prod: IProd = {
          CODPROD: data.CODPROD,
          DESCRICAO: data.DESCRICAO,
          PVENDA: data.PVENDA,
          PVENDAATAC: data.PVENDAATAC,
          DIRFOTOPROD: data.DIRFOTOPROD,
          EMBALAGEM: data.EMBALAGEM,
        };

        setProduto(prod);
        setShowProdPreco(true);
        setCodProd('');
        setTimeout(() => setShowProdPreco(false), 3000);
      } else {
      //  console.log('Produto não encontrado - formato de dados inválido');
        setShowError(true);
        setCodProd('');
        setTimeout(() => setShowError(false), 3000);
      }
    } catch (error: any) {

      
      if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      //  console.log('Erro de rede - Verificar conectividade ou URL da API');
      } else if (error.code === 'ECONNABORTED') {
      //  console.log('Timeout - API não respondeu em tempo hábil');
      }
      
      setShowError(true);
      setCodProd('');
      setTimeout(() => setShowError(false), 3000);
    }
  };

  const fetchImages = async (apiUrl: string, codProd?: string): Promise<string | void> => {
    try {
        // Se codProd for fornecido, busca imagem específica do produto
        if (codProd) {
          console.log('🌐 Buscando imagem do produto no servidor:', codProd);
          const extensions = ['jpg', 'png'];
          
          // Otimização: tenta apenas a primeira extensão por padrão
          const primaryExtension = extensions[0];
          const imageUrl = `${apiUrl}/img/${codProd}.${primaryExtension}`;
          
          try {
            // Verifica se a imagem existe fazendo uma requisição HEAD com timeout reduzido
            console.log(`🔍 Verificando imagem: ${imageUrl}`);
            const response = await axios.head(imageUrl, { timeout: 1500 });
            if (response.status === 200) {
              console.log(`✅ Imagem encontrada no servidor: ${imageUrl}`);
              return imageUrl;
            }
          } catch (headError) {
            console.log(`❌ Imagem ${primaryExtension} não encontrada, tentando ${extensions[1]}...`);
            // Se a primeira extensão falhar, tenta a segunda apenas se necessário
            if (extensions.length > 1) {
              const secondaryUrl = `${apiUrl}/img/${codProd}.${extensions[1]}`;
              try {
                console.log(`🔍 Verificando imagem: ${secondaryUrl}`);
                const response = await axios.head(secondaryUrl, { timeout: 1500 });
                if (response.status === 200) {
                  console.log(`✅ Imagem encontrada no servidor: ${secondaryUrl}`);
                  return secondaryUrl;
                }
              } catch (secondError) {
                console.log(`❌ Imagem ${extensions[1]} também não encontrada`);
                // Ignora erro e continua para fallback
              }
            }
          }
          
          // Se nenhuma extensão foi encontrada, retorna a primeira como fallback
          const fallbackUrl = `${apiUrl}/img/${codProd}.jpg`;
          console.log(`⚠️ Usando fallback para imagem: ${fallbackUrl}`);
          return fallbackUrl;
        }

        // Caso contrário, busca imagens promocionais
        console.log('🎨 Buscando imagens promocionais...');
        const response = await axios.get(`${apiUrl}/imagem/promo`, {
          timeout: 3000, // Timeout reduzido
          headers: {
            'Cache-Control': 'no-cache'
          }
        });

        const data = response.data
          .filter((image: any) => image.ativo === true)
          .map((image: any) => `${apiUrl}/img/${image.nome}`);

        console.log('✅ Imagens promocionais carregadas:', data.length);
        setImages(data);
    } catch (error) {
        console.log('❌ Erro ao buscar imagens:', error);
        if (codProd) {
          console.log(`⚠️ Retornando URL padrão para produto ${codProd}: ${apiUrl}/images/${codProd}.jpg`);
          return `${apiUrl}/images/${codProd}.jpg`; // Retorna URL padrão em caso de erro
        }
    }
  }

  useEffect(() => {
    const loadAudio = async () => {
      try {
        // Configurar o modo de áudio antes de carregar o som
        await Audio.setAudioModeAsync({
          playsInSilentModeIOS: true,
          allowsRecordingIOS: false,
          staysActiveInBackground: false,
          shouldDuckAndroid: false,
          playThroughEarpieceAndroid: false,
        });

        const { sound } = await Audio.Sound.createAsync(
          require('../../assets/audio/scanner-beep.mp3'),
          { shouldPlay: false, isLooping: false }
        );
        setSound(sound);
        console.log('✅ Áudio carregado com sucesso');
      } catch (error) {
        console.log('❌ Erro ao carregar o áudio:', error);
      }
    };

    loadAudio();

    // Limpar áudio e vídeo ao desmontar o componente
    return () => {
      if (sound) {
        sound.unloadAsync().catch(error => {
          console.log('Erro ao descarregar áudio:', error);
        });
      }
      // Limpar rotação de vídeos
      stopVideoRotation();
    };
  }, []);

  const playSound = async () => {
    try {
      if (sound) {
        console.log('🔊 Tentando reproduzir áudio...');
        
        // Verificar o status do som antes de reproduzir (sem await desnecessário)
        const status = await sound.getStatusAsync();
        //console.log('📊 Status do áudio:', status);
        
        if (status.isLoaded) {
          // Se já foi reproduzido antes, fazer replay (sem await para não bloquear)
          sound.replayAsync().then(() => {
        //    console.log('✅ Áudio reproduzido com sucesso (replay)');
          }).catch(error => {
          //  console.log('❌ Erro no replay do áudio:', error);
          });
        } else {
          // Se nunca foi reproduzido, fazer play (sem await para não bloquear)
          sound.playAsync().then(() => {
           // console.log('✅ Áudio reproduzido com sucesso (play)');
          }).catch(error => {
          //  console.log('❌ Erro no play do áudio:', error);
          });
        }
      } else {
        console.log('❌ Som não está carregado, tentando recarregar...');
        reloadSound().catch(error => {
          console.log('❌ Erro ao recarregar som:', error);
        });
      }
    } catch (error) {
      console.log('❌ Erro ao reproduzir o áudio:', error);
      // Tentar recarregar o áudio (sem await para não bloquear)
      reloadSound().catch(reloadError => {
        console.log('❌ Erro ao recarregar som:', reloadError);
      });
    }
  };

  const reloadSound = async () => {
    try {
      console.log('🔄 Recarregando áudio...');
      
      // Descarregar áudio atual se existir
      if (sound) {
        await sound.unloadAsync();
        setSound(null);
      }

      // Configurar o modo de áudio novamente
      await Audio.setAudioModeAsync({
        playsInSilentModeIOS: true,
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        shouldDuckAndroid: false,
        playThroughEarpieceAndroid: false,
      });

      // Recarregar o áudio
      const { sound: newSound } = await Audio.Sound.createAsync(
        require('../../assets/audio/scanner-beep.mp3'),
        { shouldPlay: false, isLooping: false }
      );
      
      setSound(newSound);
      console.log('✅ Áudio recarregado com sucesso');
      
      // Tentar reproduzir imediatamente
      await newSound.playAsync();
      console.log('✅ Áudio reproduzido após recarregamento');
      
    } catch (error) {
      console.log('❌ Erro ao recarregar áudio:', error);
    }
  };

  const sendDispositivoAlive = async () => {
    try {
      const ip_address = await Network.getIpAddressAsync();
      if (ip_address) {
        const response = await axios.post(`${apiUrl}/dispositivos/alive`, {
          ip_address: ip_address,
        });
      //  console.log('response.status', response.status);
      }
    } catch (error) {
    //  console.log('Erro ao enviar o alive:', error);
    }
  };

  const goToConfig = () => {
    router.replace('/password');
  }; 

  const hideNavBar = async () => {
    await NavigationBar.setVisibilityAsync("hidden");
  };

  // Funções para gerenciar vídeos promocionais
  const initializeVideoService = async (apiUrl: string) => {
    try {
    //  console.log('🎬 Inicializando VideoSyncService...');
      const videoService = VideoSyncService.getInstance();
      await videoService.initialize(apiUrl);
      setVideoSyncService(videoService);

      // Primeiro, fazer debug do diretório
      await videoService.debugVideoDirectory();

      // Verificar se precisa sincronizar vídeos
    //  console.log('🎬 Verificando se precisa sincronizar vídeos...');
      const shouldSync = await videoService.shouldSyncVideos();
    //  console.log('🎬 Precisa sincronizar?', shouldSync);
      
      if (shouldSync) {
      //  console.log('🎬 Iniciando sincronização de vídeos...');
        await videoService.syncVideos();
      //  console.log('🎬 Sincronização de vídeos concluída');
      } else {
      //  console.log('🎬 Sincronização não necessária');
      }

      // Configurar sincronização diária
    //  console.log('🎬 Configurando sincronização diária...');
      await videoService.setupDailySync();

      // Carregar vídeos locais
    //  console.log('🎬 Carregando vídeos locais...');
      await loadLocalVideos(videoService);
    } catch (error) {
      console.error('❌ Erro ao inicializar serviço de vídeos:', error);
      // Mesmo com erro, tenta carregar vídeos locais existentes
      if (videoSyncService) {
        await loadLocalVideos();
      }
    }
  };

  const loadLocalVideos = async (videoService?: VideoSyncService) => {
    try {
    //  console.log('🎬 Carregando vídeos locais...');
      const service = videoService || videoSyncService;
      if (!service) {
      //  console.log('❌ VideoSyncService não disponível');
        return;
      }

      // Primeiro, corrigir extensões de vídeos existentes se necessário
      await service.fixVideoExtensions();

      const videos = await service.getLocalVideos();
    //  console.log('🎬 Vídeos carregados do serviço:', videos);
      setAvailableVideos(videos);
      // Atualiza a ref imediatamente
      availableVideosRef.current = videos;

    //  console.log('🎬 Total de vídeos carregados:', videos.length);
      if (videos.length > 0) {
      //  console.log('📝 Vídeos disponíveis:', videos.map(path => path.split('/').pop()));
        // Verificar se os arquivos existem
        for (const video of videos) {
          try {
            const fileInfo = await FileSystem.getInfoAsync(video);
            const size = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;
          //  console.log(`📄 ${video.split('/').pop()}: existe=${fileInfo.exists}, tamanho=${size} bytes`);
          } catch (error) {
            console.error(`❌ Erro ao verificar vídeo ${video}:`, error);
          }
        }
        startVideoRotation();
      } else {
      //  console.log('⚠️ Nenhum vídeo encontrado localmente');
        // Tentar fazer debug do diretório de vídeos
        if (service) {
          await service.debugVideoDirectory();
        }
      }
    } catch (error) {
      console.error('❌ Erro ao carregar vídeos locais:', error);
    }
  };

  const startVideoRotation = () => {
    // Limpar intervalo anterior se existir
    if (videoIntervalId) {
      clearInterval(videoIntervalId);
    }

    // Função para reproduzir o próximo vídeo
    const playNextVideo = () => {
      const currentVideos = availableVideosRef.current;

      if (currentVideos.length > 0 && !showProdPreco) {
        // Usar currentVideoIndex para controlar a aleatoriedade
        const nextIndex = (currentVideoIndex + 1) % currentVideos.length;
        
        // Se chegou ao fim da lista, embaralhar a ordem
        if (nextIndex === 0) {
          // Embaralhar apenas se tiver mais de um vídeo
          if (currentVideos.length > 1) {
            const randomIndex = Math.floor(Math.random() * currentVideos.length);
            setCurrentVideoIndex(randomIndex);
          } else {
            setCurrentVideoIndex(0);
          }
        } else {
          setCurrentVideoIndex(nextIndex);
        }

        setShowVideo(true);
      }
    };

    // Reproduzir primeiro vídeo imediatamente
    playNextVideo();
    
    // Não usar mais setInterval - a rotação será controlada pelo handleVideoEnd
  //  console.log('🎬 Rotação de vídeos configurada com sucesso');
  };

  // Função para reproduzir o próximo vídeo após um delay
  const playNextVideoAfterDelay = useCallback(() => {
    const currentVideos = availableVideosRef.current;

    if (currentVideos.length > 0 && !showProdPreco) {
      // Usar currentVideoIndex para controlar a aleatoriedade
      const nextIndex = (currentVideoIndex + 1) % currentVideos.length;
      
      // Se chegou ao fim da lista, embaralhar a ordem
      if (nextIndex === 0) {
        // Embaralhar apenas se tiver mais de um vídeo
        if (currentVideos.length > 1) {
          const randomIndex = Math.floor(Math.random() * currentVideos.length);
          setCurrentVideoIndex(randomIndex);
        } else {
          setCurrentVideoIndex(0);
        }
      } else {
        setCurrentVideoIndex(nextIndex);
      }

      setShowVideo(true);
    }
  }, [showProdPreco, currentVideoIndex]);

  const stopVideoRotation = () => {
    if (videoIntervalId) {
      clearInterval(videoIntervalId);
      setVideoIntervalId(null);
    }
    
    if (videoEndTimeout) {
      clearTimeout(videoEndTimeout);
      setVideoEndTimeout(null);
    //  console.log('🎬 Timeout de próximo vídeo cancelado');
    }
    
    setShowVideo(false);
  //  console.log('🎬 Rotação de vídeos parada completamente');
  };

  const handleVideoEnd = () => {
  //  console.log('🎬 handleVideoEnd chamado - vídeo terminou naturalmente');
    setShowVideo(false);
    
    // Agendar próximo vídeo para 10 minutos depois
  //  console.log('🎬 Agendando próximo vídeo para 10 minutos');
    const timeoutId = setTimeout(() => {
    //  console.log('🎬 10 minutos passaram, reproduzindo próximo vídeo');
      playNextVideoAfterDelay();
    }, 60000 * 10);
    
    setVideoEndTimeout(timeoutId);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      hideNavBar();
    }, 7000);

    return () => {
      clearInterval(interval);
    };
  }, []);


  // Helper function to validate S3 configuration
  const validateS3Config = (endpoint: string | null, bucketName: string | null): boolean => {
    if (!endpoint || !bucketName) {
    //  console.log('❌ Configuração S3 incompleta:');
    //  console.log('  - Endpoint:', endpoint || 'Não configurado');
    //  console.log('  - Bucket Name:', bucketName || 'Não configurado');
      return false;
    }
    
  //  console.log('✅ Configuração S3 válida:');
  //  console.log('  - Endpoint:', endpoint);
  //  console.log('  - Bucket Name:', bucketName);
    return true;
  };

  // Debug function to check all AsyncStorage keys related to configuration
  const debugAsyncStorage = async () => {
    try {
    //  console.log('🔍 Debugging AsyncStorage configuration...');
      const allKeys = await AsyncStorage.getAllKeys();
    //  console.log('📋 Todas as chaves no AsyncStorage:', allKeys);
      
      const configKeys = allKeys.filter(key => 
        key.includes('api') || 
        key.includes('filial') || 
        key.includes('s3') || 
        key.includes('endpoint') || 
        key.includes('bucket')
      );
      
    //  console.log('⚙️ Chaves de configuração encontradas:', configKeys);
      
      for (const key of configKeys) {
        const value = await AsyncStorage.getItem(key);
      //  console.log(`  - ${key}: ${value || 'null'}`);
      }
    } catch (error) {
    //  console.log('❌ Erro ao debuggar AsyncStorage:', error);
    }
  };

  // Limpar estados do vídeo quando necessário
  const clearVideoStates = useCallback(() => {
    setShowVideo(false);
    setCurrentVideoIndex(0);
    
    if (videoEndTimeout) {
      clearTimeout(videoEndTimeout);
      setVideoEndTimeout(null);
    }
  }, [videoEndTimeout]);

  // Reiniciar rotação de vídeos quando a lista de vídeos mudar
  useEffect(() => {
    if (availableVideos.length > 0 && !showProdPreco) {

      
      // Limpar intervalo e timeout existentes
      if (videoIntervalId) {
        clearInterval(videoIntervalId);
        setVideoIntervalId(null);
      }
      
      if (videoEndTimeout) {
        clearTimeout(videoEndTimeout);
        setVideoEndTimeout(null);
      }
      
      // Pequeno delay para garantir que o estado foi atualizado
      setTimeout(() => {
        startVideoRotation();
      }, 100);
    }
  }, [availableVideos.length]); // Só observa o comprimento para evitar loops desnecessários

  if (showProdPreco) {
    return <ProdutoPreco produtoImagem={produtoImagem} produto={produto} />
  }

  if (showError) {
    return <ErrorPage />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.linkContainer}>
        <TouchableOpacity onLongPress={goToConfig} delayLongPress={3000}>
          <Ionicons name="cog-outline" size={30} color="black" />
        </TouchableOpacity>
      </View>
      
      {/* Indicador de sincronização em andamento */}
      {isSyncInProgress && (
        <View style={styles.syncIndicator}>
          <Ionicons name="cloud-download-outline" size={20} color="white" />
        </View>
      )}
      
      { images.length > 0 ?
        <Image
        style={styles.image}
        source={{ uri: images[currentImageIndex] }}
        transition={1000}
      /> :
        <View style={{ flex: 1, position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, width: '100%', height: '100%', backgroundColor: 'white', justifyContent: 'center', alignItems: 'center', zIndex: 10 }}>
            <Image
                style={{ width: 300, height: 200 }}
                source={require('../../assets/images/logo-sr.png')}
                placeholder={require('../../assets/images/logo-sr.png')}
                contentFit="contain"
                transition={1000}
            />
            <Animated.Text style={{ fontSize: 40, fontWeight: '900', color: '#D83235', opacity, marginTop: 20 }}>
                {texts[textIndex]}
            </Animated.Text>
        </View>
      }
      
      <TextInput
        ref={inputRef}
        value={codProd}
        style={styles.input}
        keyboardType="numeric"
        onChangeText={handleChangeText}
        onSubmitEditing={handleSubmit}
        autoFocus
        showSoftInputOnFocus={false}
      />
      {/* <TouchableOpacity
        style={{ position: 'absolute', bottom: 20, right: 20, zIndex: 20, backgroundColor: 'white' }}
        onPress={() => { 
          getProdPreco('070330717565');
        }}
      >
        <Text>OK</Text>
      </TouchableOpacity> */}

      <Animated.View
        style={{
          position: 'absolute',
          bottom: 20,
          left: '50%',
          transform: [{ translateX: -45 }, { translateY: translateY }],
          zIndex: 20
        }}
      >
        <Entypo name="arrow-bold-down" size={90} color="#D83235" />
      </Animated.View>

      {/* Reprodutor de vídeo */}
      {showVideo && availableVideos.length > 0 && (
        <VideoPlayer
          videoUri={availableVideos[currentVideoIndex]}
          onVideoEnd={handleVideoEnd}
          isVisible={showVideo}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  image: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    zIndex: 5,
    opacity: 1,
  },
  input: {
    width: '80%',
    height: 50,
    borderColor: 'E4E4E7',
    borderWidth: 1,
    borderRadius: 10,
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    opacity: 1,
    zIndex: 10,
  },
  syncIndicator: {
    position: 'absolute',
    top: 80,
    right: 20,
    zIndex: 50,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  syncText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 8,
    fontWeight: '500',
  },
  ProdutoImagem: {
    flex: 1,
    width: 300,
    height: 300,
    position: 'absolute',
    zIndex: 1,
    left: 60
  },
  overlay: {
    position: 'absolute',
    zIndex: 2,
    backgroundColor: '#c32113',
    paddingHorizontal: 20,
    height: '100%',
    width: '40%',
    right: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  descricao: {
    color: 'white',
    fontSize: 25,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  precoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  moeda: {
    color: 'white',
    fontSize: 10,
    marginRight: 10,
  },
  preco: {
    color: 'white',
    fontSize: 50,
    fontWeight: 'bold',
  },
  linkContainer: {
    position: 'absolute',
    top: 20,
    left: 20,
    zIndex: 50,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f1f1f1',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    opacity: 0.06,
  },
  expandButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 10,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f1f1f1',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    opacity: 0.06,
  },
  videoPlayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 15,
    width: '100%',
    height: '100%',
  },
});
