# Guia para Gerar APK Otimizado

## Preparação

### 1. <PERSON><PERSON><PERSON><PERSON> Imagens
```bash
# Instalar ferramentas de otimização
npm install -g imagemin-cli imagemin-webp imagemin-pngquant imagemin-mozjpeg

# Otimizar imagens (executar uma vez)
npm run optimize-images
```

### 2. Limpar Cache
```bash
# Limpar cache do Expo
npx expo export --clear

# Limpar cache do Gradle
cd android && ./gradlew clean
```

## Métodos de Build

### Método 1: EAS Build (Recomendado)
```bash
# Build otimizado com EAS
npm run build:eas
```

### Método 2: Build Local
```bash
# Build local otimizado
npm run build:light
```

### Método 3: Build Manual
```bash
# Exportar bundle
npx expo export --platform android

# Build Android
cd android
./gradlew assembleRelease
```

## Configurações Aplicadas

### Otimizações de Código:
- ✅ Hermes JS Engine habilitado
- ✅ Proguard habilitado (minificação)
- ✅ Shrink Resources habilitado
- ✅ Console.log removido em produção
- ✅ Debugger removido em produção

### Otimizações de Recursos:
- ✅ PNG Crunching habilitado
- ✅ ZIP Alignment habilitado
- ✅ Splits por ABI configurado
- ✅ Arquiteturas específicas (ARMv7, ARM64, x86, x86_64)

### Localização dos APKs:
- **APK Universal**: `android/app/build/outputs/apk/release/app-release.apk`
- **APKs por ABI**: `android/app/build/outputs/apk/release/app-*-release.apk`

## Tamanhos Esperados:
- **APK Universal**: ~30-50MB
- **APK ARM64**: ~15-25MB
- **APK ARMv7**: ~15-25MB
- **APK x86**: ~20-30MB

## Dicas Extras:

### Para Dispositivos Específicos:
```bash
# Build apenas para ARM64 (dispositivos modernos)
cd android
./gradlew assembleRelease -PreactNativeArchitectures=arm64-v8a

# Build apenas para ARMv7 (dispositivos mais antigos)
cd android
./gradlew assembleRelease -PreactNativeArchitectures=armeabi-v7a
```

### Análise do Bundle:
```bash
# Analisar tamanho do bundle
npm run analyze-bundle
```

### Verificar Tamanho:
```bash
# Windows
dir android\app\build\outputs\apk\release\*.apk

# Linux/Mac
ls -lh android/app/build/outputs/apk/release/*.apk
```

## Troubleshooting:

### Erro de Memória:
Se encontrar erro de memória, aumente a memória do Gradle:
```
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m
```

### APK Muito Grande:
1. Verifique se as imagens estão otimizadas
2. Use splits por ABI
3. Remova dependências não utilizadas
4. Verifique assets desnecessários

### Build Falhando:
1. Limpe o cache: `npx expo export --clear`
2. Limpe o Gradle: `cd android && ./gradlew clean`
3. Reconstrua: `npm run build:light`
