import { View, Text, StyleSheet } from 'react-native';
import { Image } from 'expo-image';

export default function ProdutoPreco({ produtoImagem, produto }) {
    return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', position: 'relative', backgroundColor: 'white' }}>
            <Image
            key="produto-imagem"
            style={styles.ProdutoImagem}
            source={{ uri: produtoImagem }}
            placeholder={require('../../../assets/images/logo-sr.png')}
            contentFit="cover"
            />
            <View style={styles.overlay}>
            <Text style={styles.descricao}>{produto?.DESCRICAO}</Text>
            <View style={styles.precoContainer}>
                <Text style={styles.moeda}>R$</Text>
                <Text style={styles.preco}>{produto.PVENDA ? produto.PVENDA.toFixed(2).replace('.', ',') : 'Sem dados'}</Text>
            </View>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
      flex: 1,
      position: 'relative',
      justifyContent: 'center',
      alignItems: 'center',
    },
    image: {
      flex: 1,
      width: '100%',
      height: '100%',
      backgroundColor: '#0553',
      position: 'absolute',
      zIndex: 5,
    },
    ProdutoImagem: {
      flex: 1,
      width: 300,
      height: 300,
      position: 'absolute',
      zIndex: 1,
      left: 60
    },
    overlay: {
      position: 'absolute',
      zIndex: 2,
      backgroundColor: '#c32113',
      paddingHorizontal: 20,
      height: '100%',
      width: '40%',
      right: 80,
      justifyContent: 'center',
      alignItems: 'center',
    },
    descricao: {
      color: 'white',
      fontSize: 25,
      fontWeight: 'bold',
      textAlign: 'center',
      marginBottom: 10,
    },
    precoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    moeda: {
      color: 'white',
      fontSize: 10,
      marginRight: 10,
    },
    preco: {
      color: 'white',
      fontSize: 50,
      fontWeight: 'bold',
    }
});
  