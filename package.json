{"name": "app-consulta-preco", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "eject": "npx expo prebuild", "build": "cd android && gradlew.bat assembleRelease", "build:light": "NODE_ENV=production expo export && cd android && gradlew.bat assembleRelease", "build:eas": "eas build --platform android --profile preview-light", "optimize-images": "bash optimize-images.sh", "analyze-bundle": "expo export --dump-assetmap"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@babel/runtime": "^7.27.6", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/native": "^6.0.2", "axios": "^1.7.2", "expo": "~51.0.19", "expo-asset": "~10.0.10", "expo-av": "~14.0.6", "expo-constants": "~16.0.0", "expo-file-system": "~17.0.1", "expo-font": "~12.0.7", "expo-image": "~1.13.0", "expo-keep-awake": "~13.0.2", "expo-linking": "~6.3.1", "expo-navigation-bar": "~3.0.7", "expo-network": "~6.0.1", "expo-notifications": "~0.28.19", "expo-router": "~3.5.18", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "expo-web-browser": "~13.0.3", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-web": "~0.19.10"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-remove-debugger": "^6.9.4", "jest": "^29.2.1", "jest-expo": "~51.0.3", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}