import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Script de teste para validar configuração do MinIO S3
export const testMinIOConnection = async () => {
  try {
  //  console.log('=== TESTE DE CONEXÃO MINIO S3 ===');
    
    // 1. Verificar configurações salvas
    const s3Endpoint = await AsyncStorage.getItem('@s3_endpoint');
    const s3BucketName = await AsyncStorage.getItem('@s3_bucket_name');
    
  //  console.log('Endpoint:', s3Endpoint);
  //  console.log('Bucket:', s3BucketName);
    
    if (!s3Endpoint || !s3BucketName) {
    //  console.log('❌ Configurações do S3 não encontradas');
      return false;
    }
    
    // 2. Testar acesso ao bucket (listar objetos)
    const listUrl = `${s3Endpoint}/${s3BucketName}/?list-type=2&max-keys=5`;
  //  console.log('Testando URL:', listUrl);
    
    try {
      const response = await fetch(listUrl);
    //  console.log('Status da resposta:', response.status);
      
      if (response.status === 200) {
      //  console.log('✅ Bucket acessível');
        
        // 3. Testar download de uma imagem específica (exemplo)
        const testEan = '1234567890123'; // EAN de teste
        const imageUrl = `${s3Endpoint}/${s3BucketName}/${testEan}.jpg`;
        
        const imageResponse = await fetch(imageUrl, { method: 'HEAD' });
      //  console.log('Status da imagem teste:', imageResponse.status);
        
        if (imageResponse.status === 200) {
        //  console.log('✅ Imagem de teste encontrada');
        } else {
        //  console.log('⚠️ Imagem de teste não encontrada (normal se não existir)');
        }
        
      } else {
      //  console.log('❌ Erro ao acessar bucket:', response.status);
        return false;
      }
      
    } catch (error) {
    //  console.log('❌ Erro de conectividade:', error.message);
      return false;
    }
    
    // 4. Testar criação de diretório local
    const localDir = `${FileSystem.documentDirectory}produto_images/`;
    const dirInfo = await FileSystem.getInfoAsync(localDir);
    
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(localDir, { intermediates: true });
    //  console.log('✅ Diretório local criado');
    } else {
    //  console.log('✅ Diretório local já existe');
    }
    
    // 5. Verificar espaço disponível
    const freeSpace = await FileSystem.getFreeDiskStorageAsync();
    const freeSpaceMB = freeSpace / 1024 / 1024;
  //  console.log(`💾 Espaço livre: ${freeSpaceMB.toFixed(2)} MB`);
    
    if (freeSpaceMB < 100) {
    //  console.log('⚠️ Pouco espaço disponível (menos de 100MB)');
    }
    
  //  console.log('=== TESTE CONCLUÍDO ===');
    return true;
    
  } catch (error) {
  //  console.log('❌ Erro no teste:', error);
    return false;
  }
};

// Função para limpar cache de imagens
export const clearImageCache = async () => {
  try {
    const localDir = `${FileSystem.documentDirectory}produto_images/`;
    const dirInfo = await FileSystem.getInfoAsync(localDir);
    
    if (dirInfo.exists) {
      await FileSystem.deleteAsync(localDir);
    //  console.log('🗑️ Cache de imagens limpo');
    } else {
    //  console.log('📁 Diretório de cache não existe');
    }
    
    // Remover timestamp da última sincronização
    await AsyncStorage.removeItem('@last_image_sync');
  //  console.log('🔄 Timestamp de sincronização removido');
    
  } catch (error) {
  //  console.log('❌ Erro ao limpar cache:', error);
  }
};

// Função para obter estatísticas detalhadas
export const getDetailedStats = async () => {
  try {
    const localDir = `${FileSystem.documentDirectory}produto_images/`;
    const dirInfo = await FileSystem.getInfoAsync(localDir);
    
    if (!dirInfo.exists) {
      return {
        totalImages: 0,
        totalSize: 0,
        averageSize: 0,
        lastSync: null
      };
    }
    
    const files = await FileSystem.readDirectoryAsync(localDir);
    let totalSize = 0;
    let validImages = 0;
    
    for (const file of files) {
      const filePath = `${localDir}${file}`;
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      
      if (fileInfo.exists && !fileInfo.isDirectory && (file.endsWith('.jpg') || file.endsWith('.png'))) {
        totalSize += fileInfo.size || 0;
        validImages++;
      }
    }
    
    const lastSync = await AsyncStorage.getItem('@last_image_sync');
    
    return {
      totalImages: validImages,
      totalSize,
      averageSize: validImages > 0 ? totalSize / validImages : 0,
      lastSync: lastSync ? new Date(lastSync) : null
    };
    
  } catch (error) {
  //  console.log('❌ Erro ao obter estatísticas:', error);
    return null;
  }
};
