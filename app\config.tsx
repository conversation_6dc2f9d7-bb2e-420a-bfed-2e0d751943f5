import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, Alert, TouchableOpacity, ScrollView, ActivityIndicator, BackHandler, Image, FlatList } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { router } from 'expo-router';
import { Audio } from 'expo-av';
import * as Network from 'expo-network';
import * as FileSystem from 'expo-file-system';
import { ImageSyncService } from '../services/ImageSyncService';
import { VideoSyncService } from '../services/VideoSyncService';
import { testMinIOConnection, clearImageCache, getDetailedStats } from '../utils/testMinIO';

export default function ConfigPage() {
  const [apiUrl, setApiUrl] = useState('');
  const [filial, setFilial] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [urlOk, setUrlOk] = useState(false);
  const [sound, setSound] = useState<any>(null);
  const [ip_address, setIp_address] = useState('');
  const [s3Endpoint, setS3Endpoint] = useState('');
  const [s3BucketName, setS3BucketName] = useState('');
  const [isSyncingImages, setIsSyncingImages] = useState(false);
  const [syncProgress, setSyncProgress] = useState({ current: 0, total: 0, ean: '' });
  const [showProgress, setShowProgress] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [localImages, setLocalImages] = useState<{ name: string; path: string; uri: string }[]>([]);
  const [showImageSearch, setShowImageSearch] = useState(false);
  const [backgroundSyncInProgress, setBackgroundSyncInProgress] = useState(false);
  const [progressCheckInterval, setProgressCheckInterval] = useState<NodeJS.Timeout | null>(null);
  const [isSyncingVideos, setIsSyncingVideos] = useState(false);
  const [videoSyncService, setVideoSyncService] = useState<VideoSyncService | null>(null);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedApiUrl = await AsyncStorage.getItem('@api_url');
        const storedFilial = await AsyncStorage.getItem('@filial');
        const storedS3Endpoint = await AsyncStorage.getItem('@s3_endpoint');
        const storedS3BucketName = await AsyncStorage.getItem('@s3_bucket_name');
        
       // console.log('Configurações carregadas:', { storedApiUrl, storedFilial, storedS3Endpoint, storedS3BucketName });
        
        if (storedApiUrl) {
          setApiUrl(storedApiUrl);
         // console.log('API URL carregada:', storedApiUrl);
        }
        if (storedFilial) {
          setFilial(storedFilial);
         // console.log('Filial carregada:', storedFilial);
        }
        if (storedS3Endpoint) {
          setS3Endpoint(storedS3Endpoint);
         // console.log('S3 Endpoint carregado:', storedS3Endpoint);
        }
        if (storedS3BucketName) {
          setS3BucketName(storedS3BucketName);
         // console.log('S3 Bucket carregado:', storedS3BucketName);
        }

        const ip_address = await Network.getIpAddressAsync();
        if (ip_address) setIp_address(ip_address);

        // Inicia monitoramento do progresso de sincronização em background
        checkBackgroundSyncProgress();

      } catch (error) {
      ////  console.log('Erro ao carregar as configurações:', error);
        Alert.alert('Erro', 'Falha ao carregar configurações salvas');
      }
    };

    loadSettings();
  }, []);

  // UseEffect para limpar intervals ao desmontar componente
  useEffect(() => {
    return () => {
      if (progressCheckInterval) {
        clearInterval(progressCheckInterval);
      }
    };
  }, [progressCheckInterval]);

  // UseEffect para verificar periodicamente se há sincronização em background
  useEffect(() => {
    const periodicCheck = setInterval(() => {
      if (!backgroundSyncInProgress && !isSyncingImages) {
        checkBackgroundSyncProgress();
      }
    }, 5000); // Verifica a cada 5 segundos

    return () => clearInterval(periodicCheck);
  }, [backgroundSyncInProgress, isSyncingImages]);

  useEffect(() => {
    const backAction = () => {
      /* Alert.alert("Atenção!", "Tem certeza que deseja sair?", [
        {
          text: "Cancel",
          onPress: () => null,
          style: "cancel"
        },
        { text: "YES", onPress: () => BackHandler.exitApp() }
      ]); */
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, []);

  const salvarSenhaLocalmente = async () => {
    try {
      const response = await axios.get(`${apiUrl}/senhas`);

      if (response.status === 200) {
        const { senha } = response.data;
        if (!senha) throw new Error('Senha não encontrada');

        await AsyncStorage.setItem('@senha', senha);
      }

      //console.log('response', response.data);
    } catch (error) {
     // console.log('erro ao salvar a senha:', error);
    }
  };


  async function playConexaoEstabelecida() {
    try {
      const { sound } = await Audio.Sound.createAsync( require('../assets/audio/success.mp3')
      );
  
      await sound.playAsync();
    } catch (error) {
      //console.log('Erro ao reproduzir o áudio:', error);
    }
  }

  async function playConexaoNaoEstabelecida() {
    try {
      const { sound } = await Audio.Sound.createAsync( require('../assets/audio/error.mp3')
      );
  
      await sound.playAsync();
    } catch (error) {
      //console.log('Erro ao reproduzir o áudio:', error);
    }
  }

  const saveSettings = async () => {
    try {
      if (!apiUrl || !filial) {
        Alert.alert('Erro', 'Por favor, preencha a URL da API e a Filial');
        return;
      }

      // Validar formato da URL
      if (!apiUrl.startsWith('http://') && !apiUrl.startsWith('https://')) {
        Alert.alert('Erro', 'URL deve começar com http:// ou https://');
        return;
      }

      // Validar configurações do S3 se estiverem preenchidas
      if (s3Endpoint && !s3Endpoint.startsWith('http://') && !s3Endpoint.startsWith('https://')) {
        Alert.alert('Erro', 'Endpoint do MinIO deve começar com http:// ou https://');
        return;
      }

      setUrlOk(false);

      await AsyncStorage.setItem('@api_url', apiUrl.trim());
      await AsyncStorage.setItem('@filial', filial.trim());
      await AsyncStorage.setItem('@s3_endpoint', s3Endpoint.trim());
      await AsyncStorage.setItem('@s3_bucket_name', s3BucketName.trim());
      

      
      // Verificar se foi salvo corretamente
      const savedApiUrl = await AsyncStorage.getItem('@api_url');
      const savedFilial = await AsyncStorage.getItem('@filial');
      const savedS3Endpoint = await AsyncStorage.getItem('@s3_endpoint');
      const savedS3BucketName = await AsyncStorage.getItem('@s3_bucket_name');
      
      if (savedApiUrl === apiUrl.trim() && savedFilial === filial.trim()) {
        Alert.alert('Sucesso', 'Configurações salvas com sucesso');
      } else {
        throw new Error('Falha na verificação dos dados salvos');
      }
    } catch (error) {
     // console.log('Erro ao salvar as configurações:', error);
      Alert.alert('Erro', 'Erro ao salvar as configurações');
    }
  };

  const testarApiUrl = async () => {  
    try {
      setIsLoading(true);
      await saveSettings();
      await salvarSenhaLocalmente();
  
      // Configuração da requisição com timeout de 10 segundos (10000 milissegundos)
      const response = await axios.get(apiUrl, {
        timeout: 10000, // Tempo máximo de espera em milissegundos
      });
  
      if (response.status >= 200 && response.status < 300) {
        setUrlOk(true);
        await playConexaoEstabelecida();
        Alert.alert('API disponível');
      } else {
        setUrlOk(false);
        await playConexaoNaoEstabelecida();
        Alert.alert('API indisponível ou aparelho sem conexão');
      }
  
      setIsLoading(false);
    } catch (error) {
      if (axios.isCancel(error)) {
        setIsLoading(false);
        setUrlOk(false);
      //  console.log('Requisição cancelada:', error.message);
      } else {
        setUrlOk(false);
        setIsLoading(false);
        Alert.alert('API indisponível ou aparelho sem conexão'); 
        await playConexaoNaoEstabelecida();
      //  console.log('Erro ao obter a URL da API:', error);
      }
      
      setIsLoading(false);
    }
  };

  const goToHome = async () => {
    if (!apiUrl || !filial) {
      Alert.alert('Por favor, preencha e salve as configurações');
      return;
    }
    router.replace('/');
  };

  const clear = async () => {
    try {
      await AsyncStorage.clear();

    } catch (error) {
    //  console.log('Erro ao limpar as configurações:', error);
    }
  };

  const syncImagesManually = async () => {
    try {
      if (!s3Endpoint || !s3BucketName) {
        Alert.alert('Erro', 'Configure o endpoint e bucket do MinIO S3 primeiro');
        return;
      }

      setIsSyncingImages(true);
      setShowProgress(true);
      setSyncProgress({ current: 0, total: 0, ean: '' });
      
      const s3Config = {
        s3BucketUrl: s3Endpoint.trim(),
        bucketName: s3BucketName.trim(),
        localImagesDir: 'produto_images'
      };

      // Usa singleton para evitar múltiplas instâncias
      let syncService: ImageSyncService;
      try {
        syncService = ImageSyncService.getInstance();
        syncService.updateConfig(s3Config);
      } catch (error) {
        syncService = ImageSyncService.getInstance(s3Config);
      }
      
      // Callback para atualizar o progresso
      const progressCallback = (progress: { current: number, total: number, ean: string }) => {
        setSyncProgress(progress);
      };
      
      await syncService.forceSyncImages(progressCallback);
      
      // Debug: Lista arquivos baixados
      await syncService.debugListFiles();
      
      const stats = await syncService.getStorageStats();
      
      Alert.alert(
        'Sincronização Concluída', 
        `${stats.totalImages} imagens sincronizadas\nTamanho total: ${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`
      );
      
    } catch (error) {
    //  console.log('Erro na sincronização manual:', error);
      Alert.alert('Erro', 'Falha na sincronização de imagens');
    } finally {
      setIsSyncingImages(false);
      setShowProgress(false);
      setSyncProgress({ current: 0, total: 0, ean: '' });
    }
  };

  const testS3Connection = async () => {
    const success = await testMinIOConnection();
    if (success) {
      Alert.alert('Sucesso', 'Conexão com MinIO S3 está funcionando!');
    } else {
      Alert.alert('Erro', 'Falha na conexão com MinIO S3. Verifique as configurações.');
    }
  };

  const clearImageCacheData = async () => {
    Alert.alert(
      'Confirmar',
      'Isso irá remover todas as imagens baixadas. Continuar?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Confirmar',
          onPress: async () => {
            await clearImageCache();
            Alert.alert('Sucesso', 'Cache de imagens limpo com sucesso!');
          }
        }
      ]
    );
  };

  const cleanupInvalidImages = async () => {
    try {
      if (!s3Endpoint || !s3BucketName) {
        Alert.alert('Erro', 'Configure o endpoint e bucket do MinIO S3 primeiro');
        return;
      }

      const s3Config = {
        s3BucketUrl: s3Endpoint.trim(),
        bucketName: s3BucketName.trim(),
        localImagesDir: 'produto_images'
      };

      // Usa singleton para evitar múltiplas instâncias
      let syncService: ImageSyncService;
      try {
        syncService = ImageSyncService.getInstance();
        syncService.updateConfig(s3Config);
      } catch (error) {
        syncService = ImageSyncService.getInstance(s3Config);
      }
      
      await syncService.cleanupOldImages();
      
      Alert.alert('Sucesso', 'Limpeza de arquivos inválidos concluída!');
    } catch (error) {
    //  console.log('Erro na limpeza:', error);
      Alert.alert('Erro', 'Falha na limpeza de arquivos inválidos');
    }
  };

  const showImageStats = async () => {
    const stats = await getDetailedStats();
    if (stats) {
      const sizeMB = (stats.totalSize / 1024 / 1024).toFixed(2);
      const avgSizeKB = (stats.averageSize / 1024).toFixed(2);
      const lastSyncStr = stats.lastSync ? stats.lastSync.toLocaleString('pt-BR') : 'Nunca';
      
      Alert.alert(
        'Estatísticas de Imagens',
        `Imagens: ${stats.totalImages}\nTamanho total: ${sizeMB} MB\nTamanho médio: ${avgSizeKB} KB\nÚltima sincronização: ${lastSyncStr}`
      );
    }
  };

  const searchLocalImages = async () => {
    try {
      const images: { name: string; path: string; uri: string }[] = [];
      
      // Buscar imagens nos assets
      const assetsDir = 'assets/images/';
      const assetsImages = [
        { name: 'adaptive-icon.png', path: assetsDir + 'adaptive-icon.png' },
        { name: 'favicon.png', path: assetsDir + 'favicon.png' },
        { name: 'icon.png', path: assetsDir + 'icon.png' },
        { name: 'logo-sr.png', path: assetsDir + 'logo-sr.png' },
        { name: 'splash.png', path: assetsDir + 'splash.png' },
        { name: 'catalisi-cerveja-portfolio-ambev.webp', path: assetsDir + 'comercial/catalisi-cerveja-portfolio-ambev.webp' }
      ];

      // Adicionar imagens dos assets com require
      assetsImages.forEach(img => {
        let imageSource;
        try {
          switch (img.name) {
            case 'adaptive-icon.png':
              imageSource = require('../assets/images/adaptive-icon.png');
              break;
            case 'favicon.png':
              imageSource = require('../assets/images/favicon.png');
              break;
            case 'icon.png':
              imageSource = require('../assets/images/icon.png');
              break;
            case 'logo-sr.png':
              imageSource = require('../assets/images/logo-sr.png');
              break;
            case 'splash.png':
              imageSource = require('../assets/images/splash.png');
              break;
            case 'catalisi-cerveja-portfolio-ambev.webp':
              imageSource = require('../assets/images/comercial/catalisi-cerveja-portfolio-ambev.webp');
              break;
            default:
              imageSource = null;
          }
          
          if (imageSource) {
            images.push({
              name: img.name,
              path: img.path,
              uri: imageSource
            });
          }
        } catch (error) {
        //  console.log(`Erro ao carregar imagem ${img.name}:`, error);
        }
      });

      // Buscar imagens sincronizadas do cache (se existir)
      try {
        const cacheDir = FileSystem.documentDirectory + 'produto_images/';
        const cacheInfo = await FileSystem.getInfoAsync(cacheDir);
        
        if (cacheInfo.exists && cacheInfo.isDirectory) {
          const cachedFiles = await FileSystem.readDirectoryAsync(cacheDir);
          
          for (const file of cachedFiles) {
            if (file.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
              const filePath = cacheDir + file;
              const fileInfo = await FileSystem.getInfoAsync(filePath);
              
              if (fileInfo.exists) {
                images.push({
                  name: file,
                  path: filePath,
                  uri: filePath
                });
              }
            }
          }
        }
      } catch (error) {
      //  console.log('Erro ao buscar imagens do cache:', error);
      }

      setLocalImages(images);
    //  console.log(`Encontradas ${images.length} imagens locais`);
    } catch (error) {
    //  console.log('Erro ao buscar imagens locais:', error);
      Alert.alert('Erro', 'Falha ao buscar imagens locais');
    }
  };

  const filteredImages = localImages.filter(image =>
    image.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleImageSearch = () => {
    setShowImageSearch(!showImageSearch);
    if (!showImageSearch) {
      searchLocalImages();
    }
  };

  // Função para verificar progresso de sincronização em background
  const checkBackgroundSyncProgress = async () => {
    try {
      // Verifica se há uma instância do ImageSyncService ativa
      let syncService: ImageSyncService;
      try {
        syncService = ImageSyncService.getInstance();
      } catch (error) {
        // Se não há instância, não há sincronização em background
        return;
      }

      // Verifica se há sincronização em andamento
      const isSyncing = syncService.isSyncInProgress();
      
      if (isSyncing) {
        // Carrega progresso salvo no AsyncStorage
        const progress = await syncService.getProgressFromStorage();
        
        if (progress.total > 0) {
          setBackgroundSyncInProgress(true);
          setSyncProgress(progress);
          setShowProgress(true);
          
        //  console.log('Sincronização em background detectada:', progress);
          
          // Inicia polling para acompanhar o progresso
          if (!progressCheckInterval) {
            const interval = setInterval(async () => {
              try {
                const currentProgress = await syncService.getProgressFromStorage();
                const stillSyncing = syncService.isSyncInProgress();
                
                if (stillSyncing && currentProgress.total > 0) {
                  setSyncProgress(currentProgress);
                } else {
                  // Sincronização terminou
                  setBackgroundSyncInProgress(false);
                  setShowProgress(false);
                  setSyncProgress({ current: 0, total: 0, ean: '' });
                  
                  if (progressCheckInterval) {
                    clearInterval(progressCheckInterval);
                    setProgressCheckInterval(null);
                  }
                  
                  // Mostra resultado se ainda estiver na tela
                  if (stillSyncing === false && currentProgress.current > 0) {
                    Alert.alert(
                      'Sincronização Concluída', 
                      `${currentProgress.current} imagens sincronizadas em background`
                    );
                  }
                }
              } catch (error) {
              //  console.log('Erro ao verificar progresso:', error);
              }
            }, 1000); // Verifica a cada segundo
            
            setProgressCheckInterval(interval);
          }
        }
      }
    } catch (error) {
    //  console.log('Erro ao verificar sincronização em background:', error);
    }
  };

  // Função para sincronizar vídeos promocionais
  const syncVideosPromocionais = async () => {
    try {
      if (!apiUrl) {
        Alert.alert('Erro', 'Configure a URL da API primeiro');
        return;
      }

      setIsSyncingVideos(true);
      
      // Inicializar serviço de vídeos se necessário
      let videoService = videoSyncService;
      if (!videoService) {
        videoService = VideoSyncService.getInstance();
        await videoService.initialize(apiUrl);
        setVideoSyncService(videoService);
      }

      // Debug do diretório de vídeos
      await videoService.debugVideoDirectory();
      
      // Corrigir extensões de vídeos existentes
      await videoService.fixVideoExtensions();

      // Executar sincronização
      await videoService.syncVideos();
      
      // Debug após sincronização
      const videos = await videoService.getLocalVideos();
    //  console.log('🎬 Vídeos após sincronização:', videos.length);
      
      Alert.alert('Sucesso', 'Vídeos promocionais sincronizados com sucesso!');
    } catch (error) {
    //  console.log('Erro na sincronização de vídeos:', error);
      Alert.alert('Erro', 'Falha na sincronização de vídeos promocionais');
    } finally {
      setIsSyncingVideos(false);
    }
  };

  useEffect(() => {
    // Inicia verificação de sincronização em background ao montar o componente
    checkBackgroundSyncProgress();

    // Limpa intervalo ao desmontar o componente
    return () => {
      if (progressCheckInterval) {
        clearInterval(progressCheckInterval);
      }
    };
  }, []);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.label}>URL da API:</Text>
      <TextInput
        style={styles.input}
        value={apiUrl}
        onChangeText={setApiUrl}
        placeholder="Digite a URL da API"
        autoCapitalize='none'
      />
      <Text style={styles.label}>Filial:</Text>
      <TextInput
        style={styles.input}
        value={filial}
        keyboardType="numeric"
        maxLength={2}
        onChangeText={setFilial}
        placeholder="Digite a Filial"
      />
      
      <Text style={styles.sectionTitle}>Configurações do MinIO S3:</Text>
      <Text style={styles.label}>Endpoint do MinIO:</Text>
      <TextInput
        style={styles.input}
        value={s3Endpoint}
        onChangeText={setS3Endpoint}
        placeholder="https://seu-minio-endpoint.com"
        autoCapitalize='none'
      />
      <Text style={styles.label}>Nome do Bucket:</Text>
      <TextInput
        style={styles.input}
        value={s3BucketName}
        onChangeText={setS3BucketName}
        placeholder="produtos-imagens"
        autoCapitalize='none'
      />
      
      {/* Componente de Progresso da Sincronização */}
      {showProgress && (
        <View style={styles.progressContainer}>
          <Text style={styles.progressTitle}>Sincronizando Imagens</Text>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { width: `${syncProgress.total > 0 ? (syncProgress.current / syncProgress.total) * 100 : 0}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {syncProgress.current} de {syncProgress.total} imagens
          </Text>
          <Text style={styles.progressEan}>
            Processando: {syncProgress.ean}
          </Text>
          <Text style={styles.progressPercentage}>
            {syncProgress.total > 0 ? Math.round((syncProgress.current / syncProgress.total) * 100) : 0}%
          </Text>
        </View>
      )}
      
      {/* Seção de Busca de Imagens */}
      {showImageSearch && (
        <View style={styles.imageSearchContainer}>
          <Text style={styles.sectionTitle}>Buscar Imagens Locais</Text>
          <TextInput
            style={styles.input}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Digite o nome da imagem..."
            autoCapitalize='none'
          />
          
          <Text style={styles.imageCount}>
            {filteredImages.length} imagem(ns) encontrada(s)
          </Text>
          
          <FlatList
            data={filteredImages}
            keyExtractor={(item, index) => `${item.name}-${index}`}
            numColumns={2}
            renderItem={({ item }) => (
              <View style={styles.imageItem}>
                <Image
                  source={typeof item.uri === 'string' ? { uri: item.uri } : item.uri}
                  style={styles.imagePreview}
                  resizeMode="cover"
                />
                <Text style={styles.imageName} numberOfLines={2}>
                  {item.name}
                </Text>
                <Text style={styles.imagePath} numberOfLines={1}>
                  {item.path}
                </Text>
              </View>
            )}
            style={styles.imageList}
            scrollEnabled={false}
          />
        </View>
      )}
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={saveSettings}>
            <Text style={styles.buttonText}>Salvar</Text>
        </TouchableOpacity>
        {isLoading ? (
          <ActivityIndicator />
        ) : (
          <TouchableOpacity style={styles.button} onPress={testarApiUrl}>
            <Text style={styles.buttonText}>Testar Conexão</Text>
          </TouchableOpacity>
        )}
        {isSyncingImages || backgroundSyncInProgress ? (
          <View style={[styles.button, { backgroundColor: backgroundSyncInProgress ? '#FF9500' : '#007AFF' }]}>
            <ActivityIndicator color="white" />
            <Text style={[styles.buttonText, { marginLeft: 10 }]}>
              {backgroundSyncInProgress ? 'Sincronizando em Background...' : 'Sincronizando...'}
            </Text>
          </View>
        ) : (
          <TouchableOpacity style={[styles.button, { backgroundColor: '#007AFF' }]} onPress={syncImagesManually}>
            <Text style={styles.buttonText}>Sincronizar Imagens</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity style={[styles.button, { backgroundColor: '#8E4EC6' }]} onPress={toggleImageSearch}>
          <Text style={styles.buttonText}>
            {showImageSearch ? 'Ocultar Busca de Imagens' : 'Buscar Imagens Locais'}
          </Text>
        </TouchableOpacity>
        
        {isSyncingVideos ? (
          <View style={[styles.button, { backgroundColor: '#FF6B6B' }]}>
            <ActivityIndicator color="white" />
            <Text style={[styles.buttonText, { marginLeft: 10 }]}>Sincronizando Vídeos...</Text>
          </View>
        ) : (
          <TouchableOpacity style={[styles.button, { backgroundColor: '#FF6B6B' }]} onPress={syncVideosPromocionais}>
            <Text style={styles.buttonText}>Mostrar Vídeos Promocionais</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity style={[styles.button, { backgroundColor: '#34C759' }]} onPress={testS3Connection}>
          <Text style={styles.buttonText}>Testar MinIO S3</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, { backgroundColor: '#FF9500' }]} onPress={showImageStats}>
          <Text style={styles.buttonText}>Estatísticas</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, { backgroundColor: '#FF3B30' }]} onPress={clearImageCacheData}>
          <Text style={styles.buttonText}>Limpar Cache</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, { backgroundColor: '#FF6B35' }]} onPress={cleanupInvalidImages}>
          <Text style={styles.buttonText}>Limpar Arquivos Inválidos</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button} onPress={goToHome}>
          <Text style={styles.buttonText}>Leitor</Text>
        </TouchableOpacity>
    
      </View>
          <Text style={{ color: 'black', textAlign: 'center' }}>Salve e teste a conexão antes de voltar para ao leitor</Text>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  input: {
    height: 40,
    borderColor: '#E4E4E7',
    borderRadius: 5,
    borderWidth: 1,
    marginBottom: 20,
    paddingHorizontal: 12,
    fontSize: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    rowGap: 10,
    columnGap: 10,
     marginBottom: 100
  },
  button: {
    backgroundColor: 'black',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    marginVertical: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
    color: '#333',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#e9ecef',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    textAlign: 'center',
    color: '#666',
    marginBottom: 4,
  },
  progressEan: {
    fontSize: 12,
    textAlign: 'center',
    color: '#888',
    marginBottom: 4,
  },
  progressPercentage: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#007AFF',
  },
  imageSearchContainer: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    marginVertical: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  imageCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    textAlign: 'center',
  },
  imageList: {
    maxHeight: 400,
  },
  imageItem: {
    flex: 1,
    margin: 8,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
    maxWidth: '45%',
  },
  imagePreview: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginBottom: 8,
  },
  imageName: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
    color: '#333',
  },
  imagePath: {
    fontSize: 10,
    color: '#888',
    textAlign: 'center',
  },
});