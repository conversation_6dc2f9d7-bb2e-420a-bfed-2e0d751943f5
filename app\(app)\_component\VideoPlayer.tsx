import React, { useRef, useEffect, useState } from 'react';
import { View, StyleSheet, Dimensions, Animated,Text } from 'react-native';
import { Video, ResizeMode } from 'expo-av';

interface VideoPlayerProps {
  videoUri: string;
  onPlaybackStatusUpdate?: (status: any) => void;
  shouldPlay?: boolean;
  isLooping?: boolean;
  onVideoEnd?: () => void;
  isVisible?: boolean;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUri,
  onPlaybackStatusUpdate,
  shouldPlay = true,
  isLooping = false,
  onVideoEnd,
  isVisible = true
}) => {
  const videoRef = useRef<Video>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const { width, height } = Dimensions.get('window');

  const handlePlaybackStatusUpdate = (status: any) => {


    if (onPlaybackStatusUpdate) {
      onPlaybackStatusUpdate(status);
    }

    // Verificar se o vídeo foi carregado para iniciar fade-in
    if (status.isLoaded && !isVideoLoaded) {
      setIsVideoLoaded(true);
      // Fade-in quando o vídeo carrega
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }

    // Chama onVideoEnd quando o vídeo termina
    if (status.didJustFinish && !isLooping && onVideoEnd) {
     // console.log('🎬 Vídeo terminou, iniciando fade-out');
      // Fade-out antes de chamar onVideoEnd
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        onVideoEnd();
      });
    }

    // Log de erros
    if (status.error) {
      console.error('❌ Erro na reprodução do vídeo:', status.error);
    }
  };

  // Effect para controlar visibilidade com fade
  useEffect(() => {
    if (isVisible) {
      // Reset do estado quando torna-se visível
      setIsVideoLoaded(false);
      fadeAnim.setValue(0);
    } else {
      // Fade-out quando esconde
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  useEffect(() => {

    
    if (videoRef.current && isVisible) {
      if (shouldPlay) {
        //console.log('🎬 Iniciando reprodução do vídeo');
        videoRef.current.playAsync().catch(error => {
        //  console.error('❌ Erro ao iniciar reprodução:', error);
        });
      } else {
       // console.log('🎬 Pausando vídeo');
        videoRef.current.pauseAsync().catch(error => {
         // console.error('❌ Erro ao pausar vídeo:', error);
        });
      }
    }
  }, [shouldPlay, videoUri, isVisible]);

  // Verificar se o URI é válido
  if (!videoUri || videoUri.trim() === '' || !isVisible) {
    if (!videoUri || videoUri.trim() === '') {
     // console.error('❌ URI do vídeo inválido:', videoUri);
    }
    return null;
  }

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.videoContainer}>
      <Video
        ref={videoRef}
        style={styles.video} // Removido width/height dinâmicos
        source={{ uri: videoUri }}
        shouldPlay={shouldPlay}
        isLooping={isLooping}
        resizeMode={ResizeMode.CONTAIN}
        onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
        useNativeControls={false}
        onLoad={(status) => {
         // console.log('🎬 Vídeo carregado:', status);
        }}
        onError={(error) => {
          //console.error('❌ Erro no vídeo:', error);
        }}
      />
      </View>
      <View style={styles.rodape}>
        <Text style={{fontSize:24, fontWeight:'bold', color:'red', textAlign:'center', padding:10}}>Consulte o Preço</Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
    zIndex: 50,
  },
  rodape:{
    height: '10%',
    backgroundColor:'white',
  },
  videoContainer: {
    height: '90%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: '100%',
    height: '100%',
  },
});

export default VideoPlayer;
