import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { Audio } from 'expo-av';

export default function PasswordPage() {
  const [password, setPassword] = useState('');
  const [senhaSalva, setSenhaSalva] = useState('2505');
  const [countdown, setCountdown] = useState(20);
  const [apiUrl, setApiUrl] = useState('');

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedApiUrl = await AsyncStorage.getItem('@api_url');
        const storedSenha = await AsyncStorage.getItem('@senha');

        if (storedApiUrl === null) {
          Alert.alert('Nenhuma configuração encontrada. Por favor, configure o app.');
          return;
        }

        setApiUrl(storedApiUrl);
        setSenhaSalva(storedSenha);
        
      } catch (error) {
      //  console.log('Erro ao carregar as configurações:', error);
      }
    };

    loadSettings();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prevCountdown => {
        if (prevCountdown <= 1) {
          clearInterval(timer);
          
          setTimeout(() => {
            router.replace('/(app)');
          }, 0);
        }
        return prevCountdown - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  const handleLogin = async () => {
    try {
      const response = await axios.post(`${apiUrl}/senhas/check`, {
        senha: password
      });

    //  console.log('response', response.data);
  
      if (response.status === 200) {
        await playConexaoEstabelecida();
        await AsyncStorage.setItem('@senha', password);
        router.replace('/config');
      }
    } catch (error) {
    //  console.log('Erro ao logar:', error);
      
      // Verifica a senha armazenada localmente
      try {        
        if (senhaSalva !== null && senhaSalva === password) {
        //  console.log('Senha validada localmente');
          router.replace('/config');
        } else {
          setPassword('');
          await playConexaoNaoEstabelecida();
          Alert.alert('Senha incorreta ou não encontrada no armazenamento local');
        //  console.log('Senha incorreta ou não encontrada no armazenamento local');
        }
      } catch (storageError) {
      //  console.log('Erro ao acessar o armazenamento local:', storageError);
      }
    }
  };

  async function playConexaoEstabelecida() {
    try {
      const { sound } = await Audio.Sound.createAsync( require('../assets/audio/success.mp3')
      );
  
      await sound.playAsync();
    } catch (error) {
    //  console.log('Erro ao reproduzir o áudio:', error);
    }
  }

  async function playConexaoNaoEstabelecida() {
    try {
      const { sound } = await Audio.Sound.createAsync( require('../assets/audio/error.mp3')
      );
  
      await sound.playAsync();
    } catch (error) {
    //  console.log('Erro ao reproduzir o áudio:', error);
    }
  }

  return (
    <View style={styles.container}>
      <Text style={styles.countdownText}>Tempo restante: {countdown}s</Text>
      <TextInput
        value={password}
        onChangeText={setPassword}
        placeholder="Insira sua senha"
        secureTextEntry={true}
        keyboardType="numeric"
        autoCapitalize='none'
        style={styles.input}
      />
      <TouchableOpacity
        style={styles.button}
        onPress={handleLogin}
      >
        <Text style={styles.buttonText}>Entrar</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.button}
        onPress={() => router.replace('/(app)')}
      >
        <Text style={styles.buttonText}>Voltar</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  countdownText: {
    fontSize: 16,
    marginBottom: 20,
  },
  input: {
    height: 40,
    borderColor: '#E4E4E7',
    borderRadius: 5,
    borderWidth: 1,
    marginBottom: 20,
    paddingHorizontal: 12,
    fontSize: 12,
    width: 300,
  },
  button: {
    backgroundColor: 'black',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
    width: 300,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
